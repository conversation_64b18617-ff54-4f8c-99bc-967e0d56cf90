'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { supabase, UsageLog, Transaction } from '@/lib/supabase'
import CreditsPurchase from '@/components/CreditsPurchase'
import AuthButton from '@/components/AuthButton'
import { Sparkles, Calendar, Heart, TrendingUp, CreditCard, Clock } from 'lucide-react'

export default function Dashboard() {
  const { user, userProfile, loading } = useAuth()
  const router = useRouter()
  const [usageLogs, setUsageLogs] = useState<UsageLog[]>([])
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loadingData, setLoadingData] = useState(true)

  useEffect(() => {
    if (!loading && !user) {
      router.push('/')
      return
    }

    if (user) {
      fetchUserData()
    }
  }, [user, loading, router])

  const fetchUserData = async () => {
    try {
      // Fetch usage logs
      const { data: logs, error: logsError } = await supabase
        .from('usage_logs')
        .select('*')
        .eq('user_id', user!.id)
        .order('created_at', { ascending: false })
        .limit(10)

      if (logsError) {
        console.error('Error fetching usage logs:', logsError)
      } else {
        setUsageLogs(logs || [])
      }

      // Fetch transactions
      const { data: txns, error: txnsError } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user!.id)
        .order('created_at', { ascending: false })
        .limit(10)

      if (txnsError) {
        console.error('Error fetching transactions:', txnsError)
      } else {
        setTransactions(txns || [])
      }
    } catch (error) {
      console.error('Error fetching user data:', error)
    } finally {
      setLoadingData(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Sparkles className="w-8 h-8 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          </div>
          <AuthButton />
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 py-8 space-y-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <TrendingUp className="w-8 h-8 text-blue-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Credits</h3>
                <p className="text-3xl font-bold text-blue-600">
                  {userProfile?.credits || 0}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <Calendar className="w-8 h-8 text-green-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Free Uses Today</h3>
                <p className="text-3xl font-bold text-green-600">
                  {userProfile?.daily_free_uses || 0}/3
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center gap-3">
              <Sparkles className="w-8 h-8 text-purple-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Total Analyses</h3>
                <p className="text-3xl font-bold text-purple-600">
                  {usageLogs.length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Purchase Credits */}
        <CreditsPurchase />

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Usage History */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Analyses</h3>
            
            {loadingData ? (
              <div className="animate-pulse space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            ) : usageLogs.length > 0 ? (
              <div className="space-y-3">
                {usageLogs.map((log) => (
                  <div key={log.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    {log.analysis_type === 'age' ? (
                      <Calendar className="w-5 h-5 text-blue-600" />
                    ) : (
                      <Heart className="w-5 h-5 text-pink-600" />
                    )}
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 capitalize">
                        {log.analysis_type} Analysis ({log.detail_level})
                      </div>
                      <div className="text-sm text-gray-600">
                        {log.is_free_use ? 'Free use' : `${log.credits_used} credits`}
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(log.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No analyses yet</p>
            )}
          </div>

          {/* Transaction History */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Purchase History</h3>
            
            {loadingData ? (
              <div className="animate-pulse space-y-3">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-16 bg-gray-200 rounded"></div>
                ))}
              </div>
            ) : transactions.length > 0 ? (
              <div className="space-y-3">
                {transactions.map((txn) => (
                  <div key={txn.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <CreditCard className="w-5 h-5 text-green-600" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">
                        {txn.credits_purchased} Credits
                      </div>
                      <div className="text-sm text-gray-600">
                        €{(txn.amount_cents / 100).toFixed(2)} • {txn.status}
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(txn.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">No purchases yet</p>
            )}
          </div>
        </div>

        {/* Back to App */}
        <div className="text-center">
          <button
            onClick={() => router.push('/')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Photo Analysis
          </button>
        </div>
      </main>
    </div>
  )
}
