'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { CreditCard, Loader2, Plus } from 'lucide-react'

const CREDIT_PACKAGES = [
  { credits: 10, price: 5.00, popular: false },
  { credits: 25, price: 12.50, popular: true },
  { credits: 50, price: 25.00, popular: false },
  { credits: 100, price: 50.00, popular: false },
]

export default function CreditsPurchase() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [selectedPackage, setSelectedPackage] = useState(CREDIT_PACKAGES[1])

  const handlePurchase = async (credits: number) => {
    if (!user) {
      alert('Please sign in to purchase credits')
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ credits }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout')
      }

      // Redirect to Stripe checkout
      window.location.href = data.checkoutUrl

    } catch (error) {
      console.error('Purchase error:', error)
      alert('Failed to start checkout process')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="text-center mb-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Purchase Credits
        </h3>
        <p className="text-gray-600">
          Buy credits to unlock unlimited photo analysis
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {CREDIT_PACKAGES.map((pkg) => (
          <div
            key={pkg.credits}
            className={`
              relative border-2 rounded-lg p-4 cursor-pointer transition-all
              ${selectedPackage.credits === pkg.credits
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
              }
              ${pkg.popular ? 'ring-2 ring-blue-200' : ''}
            `}
            onClick={() => setSelectedPackage(pkg)}
          >
            {pkg.popular && (
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-600 text-white text-xs font-medium px-2 py-1 rounded-full">
                  Popular
                </span>
              </div>
            )}
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900 mb-1">
                {pkg.credits}
              </div>
              <div className="text-sm text-gray-600 mb-2">Credits</div>
              <div className="text-lg font-semibold text-blue-600">
                €{pkg.price.toFixed(2)}
              </div>
              <div className="text-xs text-gray-500">
                €{(pkg.price / pkg.credits).toFixed(2)} per credit
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="text-center">
        <button
          onClick={() => handlePurchase(selectedPackage.credits)}
          disabled={loading || !user}
          className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2 mx-auto"
        >
          {loading ? (
            <>
              <Loader2 className="w-5 h-5 animate-spin" />
              Processing...
            </>
          ) : (
            <>
              <CreditCard className="w-5 h-5" />
              Purchase {selectedPackage.credits} Credits
            </>
          )}
        </button>
        
        {!user && (
          <p className="text-sm text-gray-500 mt-2">
            Please sign in to purchase credits
          </p>
        )}
      </div>

      <div className="mt-6 text-center">
        <div className="text-sm text-gray-600">
          <p className="mb-2">💳 Secure payment powered by Stripe</p>
          <p className="mb-2">🔄 Credits never expire</p>
          <p>📧 Receipt sent to your email</p>
        </div>
      </div>
    </div>
  )
}
