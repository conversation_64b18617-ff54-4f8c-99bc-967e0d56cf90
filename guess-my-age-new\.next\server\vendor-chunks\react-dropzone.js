"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-dropzone";
exports.ids = ["vendor-chunks/react-dropzone"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/index.js":
/*!******************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* reexport safe */ _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.ErrorCode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useDropzone: () => (/* binding */ useDropzone)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var file_selector__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! file-selector */ \"(ssr)/./node_modules/file-selector/dist/es2015/index.js\");\n/* harmony import */ var _utils_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/index.js */ \"(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\");\nvar _excluded = [\"children\"],\n    _excluded2 = [\"open\"],\n    _excluded3 = [\"refKey\", \"role\", \"onKeyDown\", \"onFocus\", \"onBlur\", \"onClick\", \"onDragEnter\", \"onDragOver\", \"onDragLeave\", \"onDrop\"],\n    _excluded4 = [\"refKey\", \"onChange\", \"onClick\"];\n\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n/* eslint prefer-template: 0 */\n\n\n\n\n/**\n * Convenience wrapper component for the `useDropzone` hook\n *\n * ```jsx\n * <Dropzone>\n *   {({getRootProps, getInputProps}) => (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag 'n' drop some files here, or click to select files</p>\n *     </div>\n *   )}\n * </Dropzone>\n * ```\n */\n\nvar Dropzone = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function (_ref, ref) {\n  var children = _ref.children,\n      params = _objectWithoutProperties(_ref, _excluded);\n\n  var _useDropzone = useDropzone(params),\n      open = _useDropzone.open,\n      props = _objectWithoutProperties(_useDropzone, _excluded2);\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function () {\n    return {\n      open: open\n    };\n  }, [open]); // TODO: Figure out why react-styleguidist cannot create docs if we don't return a jsx element\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children(_objectSpread(_objectSpread({}, props), {}, {\n    open: open\n  })));\n});\nDropzone.displayName = \"Dropzone\"; // Add default props for react-docgen\n\nvar defaultProps = {\n  disabled: false,\n  getFilesFromEvent: file_selector__WEBPACK_IMPORTED_MODULE_1__.fromEvent,\n  maxSize: Infinity,\n  minSize: 0,\n  multiple: true,\n  maxFiles: 0,\n  preventDropOnDocument: true,\n  noClick: false,\n  noKeyboard: false,\n  noDrag: false,\n  noDragEventsBubbling: false,\n  validator: null,\n  useFsAccessApi: false,\n  autoFocus: false\n};\nDropzone.defaultProps = defaultProps;\nDropzone.propTypes = {\n  /**\n   * Render function that exposes the dropzone state and prop getter fns\n   *\n   * @param {object} params\n   * @param {Function} params.getRootProps Returns the props you should apply to the root drop container you render\n   * @param {Function} params.getInputProps Returns the props you should apply to hidden file input you render\n   * @param {Function} params.open Open the native file selection dialog\n   * @param {boolean} params.isFocused Dropzone area is in focus\n   * @param {boolean} params.isFileDialogActive File dialog is opened\n   * @param {boolean} params.isDragActive Active drag is in progress\n   * @param {boolean} params.isDragAccept Dragged files are accepted\n   * @param {boolean} params.isDragReject Some dragged files are rejected\n   * @param {File[]} params.acceptedFiles Accepted files\n   * @param {FileRejection[]} params.fileRejections Rejected files and why they were rejected\n   */\n  children: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Set accepted file types.\n   * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n   * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n   * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n   * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n   */\n  accept: prop_types__WEBPACK_IMPORTED_MODULE_3__.objectOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.arrayOf(prop_types__WEBPACK_IMPORTED_MODULE_3__.string)),\n\n  /**\n   * Allow drag 'n' drop (or selection from the file dialog) of multiple files\n   */\n  multiple: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * If false, allow dropped items to take over the current browser window\n   */\n  preventDropOnDocument: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * If true, disables click to open the native file selection dialog\n   */\n  noClick: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * If true, disables SPACE/ENTER to open the native file selection dialog.\n   * Note that it also stops tracking the focus state.\n   */\n  noKeyboard: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * If true, disables drag 'n' drop\n   */\n  noDrag: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * If true, stops drag event propagation to parents\n   */\n  noDragEventsBubbling: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * Minimum file size (in bytes)\n   */\n  minSize: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n\n  /**\n   * Maximum file size (in bytes)\n   */\n  maxSize: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n\n  /**\n   * Maximum accepted number of files\n   * The default value is 0 which means there is no limitation to how many files are accepted.\n   */\n  maxFiles: prop_types__WEBPACK_IMPORTED_MODULE_3__.number,\n\n  /**\n   * Enable/disable the dropzone\n   */\n  disabled: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * Use this to provide a custom file aggregator\n   *\n   * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  getFilesFromEvent: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when closing the file dialog with no selection\n   */\n  onFileDialogCancel: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when opening the file dialog\n   */\n  onFileDialogOpen: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n   * to open the file picker instead of using an `<input type=\"file\">` click event.\n   */\n  useFsAccessApi: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * Set to true to focus the root element on render\n   */\n  autoFocus: prop_types__WEBPACK_IMPORTED_MODULE_3__.bool,\n\n  /**\n   * Cb for when the `dragenter` event occurs.\n   *\n   * @param {DragEvent} event\n   */\n  onDragEnter: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when the `dragleave` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragLeave: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when the `dragover` event occurs\n   *\n   * @param {DragEvent} event\n   */\n  onDragOver: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n   *\n   * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n   * `accept` must be a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) or a valid file extension.\n   * If `multiple` is set to false and additional files are dropped,\n   * all files besides the first will be rejected.\n   * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n   *\n   * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n   * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n   *\n   * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n   * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n   *\n   * ```js\n   * function onDrop(acceptedFiles) {\n   *   const req = request.post('/upload')\n   *   acceptedFiles.forEach(file => {\n   *     req.attach(file.name, file)\n   *   })\n   *   req.end(callback)\n   * }\n   * ```\n   *\n   * @param {File[]} acceptedFiles\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n   */\n  onDrop: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are accepted, this callback is not invoked.\n   *\n   * @param {File[]} files\n   * @param {(DragEvent|Event)} event\n   */\n  onDropAccepted: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when the `drop` event occurs.\n   * Note that if no files are rejected, this callback is not invoked.\n   *\n   * @param {FileRejection[]} fileRejections\n   * @param {(DragEvent|Event)} event\n   */\n  onDropRejected: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Cb for when there's some error from any of the promises.\n   *\n   * @param {Error} error\n   */\n  onError: prop_types__WEBPACK_IMPORTED_MODULE_3__.func,\n\n  /**\n   * Custom validation function. It must return null if there's no errors.\n   * @param {File} file\n   * @returns {FileError|FileError[]|null}\n   */\n  validator: prop_types__WEBPACK_IMPORTED_MODULE_3__.func\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dropzone);\n/**\n * A function that is invoked for the `dragenter`,\n * `dragover` and `dragleave` events.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dragCb\n * @param {DragEvent} event\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are not files (such as link, text, etc.).\n *\n * @callback dropCb\n * @param {File[]} acceptedFiles List of accepted files\n * @param {FileRejection[]} fileRejections List of rejected files and why they were rejected\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n * It is not invoked if the items are files (such as link, text, etc.).\n *\n * @callback dropAcceptedCb\n * @param {File[]} files List of accepted files that meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is invoked for the `drop` or input change event.\n *\n * @callback dropRejectedCb\n * @param {File[]} files List of rejected files that do not meet the given criteria\n * (`accept`, `multiple`, `minSize`, `maxSize`)\n * @param {(DragEvent|Event)} event A drag event or input change event (if files were selected via the file dialog)\n */\n\n/**\n * A function that is used aggregate files,\n * in a asynchronous fashion, from drag or input change events.\n *\n * @callback getFilesFromEvent\n * @param {(DragEvent|Event|Array<FileSystemFileHandle>)} event A drag event or input change event (if files were selected via the file dialog)\n * @returns {(File[]|Promise<File[]>)}\n */\n\n/**\n * An object with the current dropzone state.\n *\n * @typedef {object} DropzoneState\n * @property {boolean} isFocused Dropzone area is in focus\n * @property {boolean} isFileDialogActive File dialog is opened\n * @property {boolean} isDragActive Active drag is in progress\n * @property {boolean} isDragAccept Dragged files are accepted\n * @property {boolean} isDragReject Some dragged files are rejected\n * @property {File[]} acceptedFiles Accepted files\n * @property {FileRejection[]} fileRejections Rejected files and why they were rejected\n */\n\n/**\n * An object with the dropzone methods.\n *\n * @typedef {object} DropzoneMethods\n * @property {Function} getRootProps Returns the props you should apply to the root drop container you render\n * @property {Function} getInputProps Returns the props you should apply to hidden file input you render\n * @property {Function} open Open the native file selection dialog\n */\n\nvar initialState = {\n  isFocused: false,\n  isFileDialogActive: false,\n  isDragActive: false,\n  isDragAccept: false,\n  isDragReject: false,\n  acceptedFiles: [],\n  fileRejections: []\n};\n/**\n * A React hook that creates a drag 'n' drop area.\n *\n * ```jsx\n * function MyDropzone(props) {\n *   const {getRootProps, getInputProps} = useDropzone({\n *     onDrop: acceptedFiles => {\n *       // do something with the File objects, e.g. upload to some server\n *     }\n *   });\n *   return (\n *     <div {...getRootProps()}>\n *       <input {...getInputProps()} />\n *       <p>Drag and drop some files here, or click to select files</p>\n *     </div>\n *   )\n * }\n * ```\n *\n * @function useDropzone\n *\n * @param {object} props\n * @param {import(\"./utils\").AcceptProp} [props.accept] Set accepted file types.\n * Checkout https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker types option for more information.\n * Keep in mind that mime type determination is not reliable across platforms. CSV files,\n * for example, are reported as text/plain under macOS but as application/vnd.ms-excel under\n * Windows. In some cases there might not be a mime type set at all (https://github.com/react-dropzone/react-dropzone/issues/276).\n * @param {boolean} [props.multiple=true] Allow drag 'n' drop (or selection from the file dialog) of multiple files\n * @param {boolean} [props.preventDropOnDocument=true] If false, allow dropped items to take over the current browser window\n * @param {boolean} [props.noClick=false] If true, disables click to open the native file selection dialog\n * @param {boolean} [props.noKeyboard=false] If true, disables SPACE/ENTER to open the native file selection dialog.\n * Note that it also stops tracking the focus state.\n * @param {boolean} [props.noDrag=false] If true, disables drag 'n' drop\n * @param {boolean} [props.noDragEventsBubbling=false] If true, stops drag event propagation to parents\n * @param {number} [props.minSize=0] Minimum file size (in bytes)\n * @param {number} [props.maxSize=Infinity] Maximum file size (in bytes)\n * @param {boolean} [props.disabled=false] Enable/disable the dropzone\n * @param {getFilesFromEvent} [props.getFilesFromEvent] Use this to provide a custom file aggregator\n * @param {Function} [props.onFileDialogCancel] Cb for when closing the file dialog with no selection\n * @param {boolean} [props.useFsAccessApi] Set to true to use the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API\n * to open the file picker instead of using an `<input type=\"file\">` click event.\n * @param {boolean} autoFocus Set to true to auto focus the root element.\n * @param {Function} [props.onFileDialogOpen] Cb for when opening the file dialog\n * @param {dragCb} [props.onDragEnter] Cb for when the `dragenter` event occurs.\n * @param {dragCb} [props.onDragLeave] Cb for when the `dragleave` event occurs\n * @param {dragCb} [props.onDragOver] Cb for when the `dragover` event occurs\n * @param {dropCb} [props.onDrop] Cb for when the `drop` event occurs.\n * Note that this callback is invoked after the `getFilesFromEvent` callback is done.\n *\n * Files are accepted or rejected based on the `accept`, `multiple`, `minSize` and `maxSize` props.\n * `accept` must be an object with keys as a valid [MIME type](http://www.iana.org/assignments/media-types/media-types.xhtml) according to [input element specification](https://www.w3.org/wiki/HTML/Elements/input/file) and the value an array of file extensions (optional).\n * If `multiple` is set to false and additional files are dropped,\n * all files besides the first will be rejected.\n * Any file which does not have a size in the [`minSize`, `maxSize`] range, will be rejected as well.\n *\n * Note that the `onDrop` callback will always be invoked regardless if the dropped files were accepted or rejected.\n * If you'd like to react to a specific scenario, use the `onDropAccepted`/`onDropRejected` props.\n *\n * `onDrop` will provide you with an array of [File](https://developer.mozilla.org/en-US/docs/Web/API/File) objects which you can then process and send to a server.\n * For example, with [SuperAgent](https://github.com/visionmedia/superagent) as a http/ajax library:\n *\n * ```js\n * function onDrop(acceptedFiles) {\n *   const req = request.post('/upload')\n *   acceptedFiles.forEach(file => {\n *     req.attach(file.name, file)\n *   })\n *   req.end(callback)\n * }\n * ```\n * @param {dropAcceptedCb} [props.onDropAccepted]\n * @param {dropRejectedCb} [props.onDropRejected]\n * @param {(error: Error) => void} [props.onError]\n *\n * @returns {DropzoneState & DropzoneMethods}\n */\n\nfunction useDropzone() {\n  var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n  var _defaultProps$props = _objectSpread(_objectSpread({}, defaultProps), props),\n      accept = _defaultProps$props.accept,\n      disabled = _defaultProps$props.disabled,\n      getFilesFromEvent = _defaultProps$props.getFilesFromEvent,\n      maxSize = _defaultProps$props.maxSize,\n      minSize = _defaultProps$props.minSize,\n      multiple = _defaultProps$props.multiple,\n      maxFiles = _defaultProps$props.maxFiles,\n      onDragEnter = _defaultProps$props.onDragEnter,\n      onDragLeave = _defaultProps$props.onDragLeave,\n      onDragOver = _defaultProps$props.onDragOver,\n      onDrop = _defaultProps$props.onDrop,\n      onDropAccepted = _defaultProps$props.onDropAccepted,\n      onDropRejected = _defaultProps$props.onDropRejected,\n      onFileDialogCancel = _defaultProps$props.onFileDialogCancel,\n      onFileDialogOpen = _defaultProps$props.onFileDialogOpen,\n      useFsAccessApi = _defaultProps$props.useFsAccessApi,\n      autoFocus = _defaultProps$props.autoFocus,\n      preventDropOnDocument = _defaultProps$props.preventDropOnDocument,\n      noClick = _defaultProps$props.noClick,\n      noKeyboard = _defaultProps$props.noKeyboard,\n      noDrag = _defaultProps$props.noDrag,\n      noDragEventsBubbling = _defaultProps$props.noDragEventsBubbling,\n      onError = _defaultProps$props.onError,\n      validator = _defaultProps$props.validator;\n\n  var acceptAttr = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.acceptPropAsAcceptAttr)(accept);\n  }, [accept]);\n  var pickerTypes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.pickerOptionsFromAccept)(accept);\n  }, [accept]);\n  var onFileDialogOpenCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return typeof onFileDialogOpen === \"function\" ? onFileDialogOpen : noop;\n  }, [onFileDialogOpen]);\n  var onFileDialogCancelCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return typeof onFileDialogCancel === \"function\" ? onFileDialogCancel : noop;\n  }, [onFileDialogCancel]);\n  /**\n   * @constant\n   * @type {React.MutableRefObject<HTMLElement>}\n   */\n\n  var rootRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  var _useReducer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, initialState),\n      _useReducer2 = _slicedToArray(_useReducer, 2),\n      state = _useReducer2[0],\n      dispatch = _useReducer2[1];\n\n  var isFocused = state.isFocused,\n      isFileDialogActive = state.isFileDialogActive;\n  var fsAccessApiWorksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(typeof window !== \"undefined\" && window.isSecureContext && useFsAccessApi && (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.canUseFileSystemAccessAPI)()); // Update file dialog active state when the window is focused on\n\n  var onWindowFocus = function onWindowFocus() {\n    // Execute the timeout only if the file dialog is opened in the browser\n    if (!fsAccessApiWorksRef.current && isFileDialogActive) {\n      setTimeout(function () {\n        if (inputRef.current) {\n          var files = inputRef.current.files;\n\n          if (!files.length) {\n            dispatch({\n              type: \"closeDialog\"\n            });\n            onFileDialogCancelCb();\n          }\n        }\n      }, 300);\n    }\n  };\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    window.addEventListener(\"focus\", onWindowFocus, false);\n    return function () {\n      window.removeEventListener(\"focus\", onWindowFocus, false);\n    };\n  }, [inputRef, isFileDialogActive, onFileDialogCancelCb, fsAccessApiWorksRef]);\n  var dragTargetsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n\n  var onDocumentDrop = function onDocumentDrop(event) {\n    if (rootRef.current && rootRef.current.contains(event.target)) {\n      // If we intercepted an event for our instance, let it propagate down to the instance's onDrop handler\n      return;\n    }\n\n    event.preventDefault();\n    dragTargetsRef.current = [];\n  };\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (preventDropOnDocument) {\n      document.addEventListener(\"dragover\", _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.onDocumentDragOver, false);\n      document.addEventListener(\"drop\", onDocumentDrop, false);\n    }\n\n    return function () {\n      if (preventDropOnDocument) {\n        document.removeEventListener(\"dragover\", _utils_index_js__WEBPACK_IMPORTED_MODULE_2__.onDocumentDragOver);\n        document.removeEventListener(\"drop\", onDocumentDrop);\n      }\n    };\n  }, [rootRef, preventDropOnDocument]); // Auto focus the root when autoFocus is true\n\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n    if (!disabled && autoFocus && rootRef.current) {\n      rootRef.current.focus();\n    }\n\n    return function () {};\n  }, [rootRef, autoFocus, disabled]);\n  var onErrCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (e) {\n    if (onError) {\n      onError(e);\n    } else {\n      // Let the user know something's gone wrong if they haven't provided the onError cb.\n      console.error(e);\n    }\n  }, [onError]);\n  var onDragEnterCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [].concat(_toConsumableArray(dragTargetsRef.current), [event.target]);\n\n    if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        var fileCount = files.length;\n        var isDragAccept = fileCount > 0 && (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.allFilesAccepted)({\n          files: files,\n          accept: acceptAttr,\n          minSize: minSize,\n          maxSize: maxSize,\n          multiple: multiple,\n          maxFiles: maxFiles,\n          validator: validator\n        });\n        var isDragReject = fileCount > 0 && !isDragAccept;\n        dispatch({\n          isDragAccept: isDragAccept,\n          isDragReject: isDragReject,\n          isDragActive: true,\n          type: \"setDraggedFiles\"\n        });\n\n        if (onDragEnter) {\n          onDragEnter(event);\n        }\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n  }, [getFilesFromEvent, onDragEnter, onErrCb, noDragEventsBubbling, acceptAttr, minSize, maxSize, multiple, maxFiles, validator]);\n  var onDragOverCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event);\n    var hasFiles = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event);\n\n    if (hasFiles && event.dataTransfer) {\n      try {\n        event.dataTransfer.dropEffect = \"copy\";\n      } catch (_unused) {}\n      /* eslint-disable-line no-empty */\n\n    }\n\n    if (hasFiles && onDragOver) {\n      onDragOver(event);\n    }\n\n    return false;\n  }, [onDragOver, noDragEventsBubbling]);\n  var onDragLeaveCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault();\n    event.persist();\n    stopPropagation(event); // Only deactivate once the dropzone and all children have been left\n\n    var targets = dragTargetsRef.current.filter(function (target) {\n      return rootRef.current && rootRef.current.contains(target);\n    }); // Make sure to remove a target present multiple times only once\n    // (Firefox may fire dragenter/dragleave multiple times on the same element)\n\n    var targetIdx = targets.indexOf(event.target);\n\n    if (targetIdx !== -1) {\n      targets.splice(targetIdx, 1);\n    }\n\n    dragTargetsRef.current = targets;\n\n    if (targets.length > 0) {\n      return;\n    }\n\n    dispatch({\n      type: \"setDraggedFiles\",\n      isDragActive: false,\n      isDragAccept: false,\n      isDragReject: false\n    });\n\n    if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event) && onDragLeave) {\n      onDragLeave(event);\n    }\n  }, [rootRef, onDragLeave, noDragEventsBubbling]);\n  var setFiles = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (files, event) {\n    var acceptedFiles = [];\n    var fileRejections = [];\n    files.forEach(function (file) {\n      var _fileAccepted = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.fileAccepted)(file, acceptAttr),\n          _fileAccepted2 = _slicedToArray(_fileAccepted, 2),\n          accepted = _fileAccepted2[0],\n          acceptError = _fileAccepted2[1];\n\n      var _fileMatchSize = (0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.fileMatchSize)(file, minSize, maxSize),\n          _fileMatchSize2 = _slicedToArray(_fileMatchSize, 2),\n          sizeMatch = _fileMatchSize2[0],\n          sizeError = _fileMatchSize2[1];\n\n      var customErrors = validator ? validator(file) : null;\n\n      if (accepted && sizeMatch && !customErrors) {\n        acceptedFiles.push(file);\n      } else {\n        var errors = [acceptError, sizeError];\n\n        if (customErrors) {\n          errors = errors.concat(customErrors);\n        }\n\n        fileRejections.push({\n          file: file,\n          errors: errors.filter(function (e) {\n            return e;\n          })\n        });\n      }\n    });\n\n    if (!multiple && acceptedFiles.length > 1 || multiple && maxFiles >= 1 && acceptedFiles.length > maxFiles) {\n      // Reject everything and empty accepted files\n      acceptedFiles.forEach(function (file) {\n        fileRejections.push({\n          file: file,\n          errors: [_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.TOO_MANY_FILES_REJECTION]\n        });\n      });\n      acceptedFiles.splice(0);\n    }\n\n    dispatch({\n      acceptedFiles: acceptedFiles,\n      fileRejections: fileRejections,\n      isDragReject: fileRejections.length > 0,\n      type: \"setFiles\"\n    });\n\n    if (onDrop) {\n      onDrop(acceptedFiles, fileRejections, event);\n    }\n\n    if (fileRejections.length > 0 && onDropRejected) {\n      onDropRejected(fileRejections, event);\n    }\n\n    if (acceptedFiles.length > 0 && onDropAccepted) {\n      onDropAccepted(acceptedFiles, event);\n    }\n  }, [dispatch, multiple, acceptAttr, minSize, maxSize, maxFiles, onDrop, onDropAccepted, onDropRejected, validator]);\n  var onDropCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.preventDefault(); // Persist here because we need the event later after getFilesFromEvent() is done\n\n    event.persist();\n    stopPropagation(event);\n    dragTargetsRef.current = [];\n\n    if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isEvtWithFiles)(event)) {\n      Promise.resolve(getFilesFromEvent(event)).then(function (files) {\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isPropagationStopped)(event) && !noDragEventsBubbling) {\n          return;\n        }\n\n        setFiles(files, event);\n      }).catch(function (e) {\n        return onErrCb(e);\n      });\n    }\n\n    dispatch({\n      type: \"reset\"\n    });\n  }, [getFilesFromEvent, setFiles, onErrCb, noDragEventsBubbling]); // Fn for opening the file dialog programmatically\n\n  var openFileDialog = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    // No point to use FS access APIs if context is not secure\n    // https://developer.mozilla.org/en-US/docs/Web/Security/Secure_Contexts#feature_detection\n    if (fsAccessApiWorksRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb(); // https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n\n      var opts = {\n        multiple: multiple,\n        types: pickerTypes\n      };\n      window.showOpenFilePicker(opts).then(function (handles) {\n        return getFilesFromEvent(handles);\n      }).then(function (files) {\n        setFiles(files, null);\n        dispatch({\n          type: \"closeDialog\"\n        });\n      }).catch(function (e) {\n        // AbortError means the user canceled\n        if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isAbort)(e)) {\n          onFileDialogCancelCb(e);\n          dispatch({\n            type: \"closeDialog\"\n          });\n        } else if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isSecurityError)(e)) {\n          fsAccessApiWorksRef.current = false; // CORS, so cannot use this API\n          // Try using the input\n\n          if (inputRef.current) {\n            inputRef.current.value = null;\n            inputRef.current.click();\n          } else {\n            onErrCb(new Error(\"Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided.\"));\n          }\n        } else {\n          onErrCb(e);\n        }\n      });\n      return;\n    }\n\n    if (inputRef.current) {\n      dispatch({\n        type: \"openDialog\"\n      });\n      onFileDialogOpenCb();\n      inputRef.current.value = null;\n      inputRef.current.click();\n    }\n  }, [dispatch, onFileDialogOpenCb, onFileDialogCancelCb, useFsAccessApi, setFiles, onErrCb, pickerTypes, multiple]); // Cb to open the file dialog when SPACE/ENTER occurs on the dropzone\n\n  var onKeyDownCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    // Ignore keyboard events bubbling up the DOM tree\n    if (!rootRef.current || !rootRef.current.isEqualNode(event.target)) {\n      return;\n    }\n\n    if (event.key === \" \" || event.key === \"Enter\" || event.keyCode === 32 || event.keyCode === 13) {\n      event.preventDefault();\n      openFileDialog();\n    }\n  }, [rootRef, openFileDialog]); // Update focus state for the dropzone\n\n  var onFocusCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    dispatch({\n      type: \"focus\"\n    });\n  }, []);\n  var onBlurCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    dispatch({\n      type: \"blur\"\n    });\n  }, []); // Cb to open the file dialog when click occurs on the dropzone\n\n  var onClickCb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    if (noClick) {\n      return;\n    } // In IE11/Edge the file-browser dialog is blocking, therefore, use setTimeout()\n    // to ensure React can handle state changes\n    // See: https://github.com/react-dropzone/react-dropzone/issues/450\n\n\n    if ((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.isIeOrEdge)()) {\n      setTimeout(openFileDialog, 0);\n    } else {\n      openFileDialog();\n    }\n  }, [noClick, openFileDialog]);\n\n  var composeHandler = function composeHandler(fn) {\n    return disabled ? null : fn;\n  };\n\n  var composeKeyboardHandler = function composeKeyboardHandler(fn) {\n    return noKeyboard ? null : composeHandler(fn);\n  };\n\n  var composeDragHandler = function composeDragHandler(fn) {\n    return noDrag ? null : composeHandler(fn);\n  };\n\n  var stopPropagation = function stopPropagation(event) {\n    if (noDragEventsBubbling) {\n      event.stopPropagation();\n    }\n  };\n\n  var getRootProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return function () {\n      var _ref2 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref2$refKey = _ref2.refKey,\n          refKey = _ref2$refKey === void 0 ? \"ref\" : _ref2$refKey,\n          role = _ref2.role,\n          onKeyDown = _ref2.onKeyDown,\n          onFocus = _ref2.onFocus,\n          onBlur = _ref2.onBlur,\n          onClick = _ref2.onClick,\n          onDragEnter = _ref2.onDragEnter,\n          onDragOver = _ref2.onDragOver,\n          onDragLeave = _ref2.onDragLeave,\n          onDrop = _ref2.onDrop,\n          rest = _objectWithoutProperties(_ref2, _excluded3);\n\n      return _objectSpread(_objectSpread(_defineProperty({\n        onKeyDown: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onKeyDown, onKeyDownCb)),\n        onFocus: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onFocus, onFocusCb)),\n        onBlur: composeKeyboardHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onBlur, onBlurCb)),\n        onClick: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onClick, onClickCb)),\n        onDragEnter: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragEnter, onDragEnterCb)),\n        onDragOver: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragOver, onDragOverCb)),\n        onDragLeave: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDragLeave, onDragLeaveCb)),\n        onDrop: composeDragHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onDrop, onDropCb)),\n        role: typeof role === \"string\" && role !== \"\" ? role : \"presentation\"\n      }, refKey, rootRef), !disabled && !noKeyboard ? {\n        tabIndex: 0\n      } : {}), rest);\n    };\n  }, [rootRef, onKeyDownCb, onFocusCb, onBlurCb, onClickCb, onDragEnterCb, onDragOverCb, onDragLeaveCb, onDropCb, noKeyboard, noDrag, disabled]);\n  var onInputElementClick = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    event.stopPropagation();\n  }, []);\n  var getInputProps = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n    return function () {\n      var _ref3 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {},\n          _ref3$refKey = _ref3.refKey,\n          refKey = _ref3$refKey === void 0 ? \"ref\" : _ref3$refKey,\n          onChange = _ref3.onChange,\n          onClick = _ref3.onClick,\n          rest = _objectWithoutProperties(_ref3, _excluded4);\n\n      var inputProps = _defineProperty({\n        accept: acceptAttr,\n        multiple: multiple,\n        type: \"file\",\n        style: {\n          border: 0,\n          clip: \"rect(0, 0, 0, 0)\",\n          clipPath: \"inset(50%)\",\n          height: \"1px\",\n          margin: \"0 -1px -1px 0\",\n          overflow: \"hidden\",\n          padding: 0,\n          position: \"absolute\",\n          width: \"1px\",\n          whiteSpace: \"nowrap\"\n        },\n        onChange: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onChange, onDropCb)),\n        onClick: composeHandler((0,_utils_index_js__WEBPACK_IMPORTED_MODULE_2__.composeEventHandlers)(onClick, onInputElementClick)),\n        tabIndex: -1\n      }, refKey, inputRef);\n\n      return _objectSpread(_objectSpread({}, inputProps), rest);\n    };\n  }, [inputRef, accept, multiple, onDropCb, disabled]);\n  return _objectSpread(_objectSpread({}, state), {}, {\n    isFocused: isFocused && !disabled,\n    getRootProps: getRootProps,\n    getInputProps: getInputProps,\n    rootRef: rootRef,\n    inputRef: inputRef,\n    open: composeHandler(openFileDialog)\n  });\n}\n/**\n * @param {DropzoneState} state\n * @param {{type: string} & DropzoneState} action\n * @returns {DropzoneState}\n */\n\nfunction reducer(state, action) {\n  /* istanbul ignore next */\n  switch (action.type) {\n    case \"focus\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: true\n      });\n\n    case \"blur\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFocused: false\n      });\n\n    case \"openDialog\":\n      return _objectSpread(_objectSpread({}, initialState), {}, {\n        isFileDialogActive: true\n      });\n\n    case \"closeDialog\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isFileDialogActive: false\n      });\n\n    case \"setDraggedFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        isDragActive: action.isDragActive,\n        isDragAccept: action.isDragAccept,\n        isDragReject: action.isDragReject\n      });\n\n    case \"setFiles\":\n      return _objectSpread(_objectSpread({}, state), {}, {\n        acceptedFiles: action.acceptedFiles,\n        fileRejections: action.fileRejections,\n        isDragReject: action.isDragReject\n      });\n\n    case \"reset\":\n      return _objectSpread({}, initialState);\n\n    default:\n      return state;\n  }\n}\n\nfunction noop() {}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js":
/*!************************************************************!*\
  !*** ./node_modules/react-dropzone/dist/es/utils/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   FILE_INVALID_TYPE: () => (/* binding */ FILE_INVALID_TYPE),\n/* harmony export */   FILE_TOO_LARGE: () => (/* binding */ FILE_TOO_LARGE),\n/* harmony export */   FILE_TOO_SMALL: () => (/* binding */ FILE_TOO_SMALL),\n/* harmony export */   TOO_MANY_FILES: () => (/* binding */ TOO_MANY_FILES),\n/* harmony export */   TOO_MANY_FILES_REJECTION: () => (/* binding */ TOO_MANY_FILES_REJECTION),\n/* harmony export */   acceptPropAsAcceptAttr: () => (/* binding */ acceptPropAsAcceptAttr),\n/* harmony export */   allFilesAccepted: () => (/* binding */ allFilesAccepted),\n/* harmony export */   canUseFileSystemAccessAPI: () => (/* binding */ canUseFileSystemAccessAPI),\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers),\n/* harmony export */   fileAccepted: () => (/* binding */ fileAccepted),\n/* harmony export */   fileMatchSize: () => (/* binding */ fileMatchSize),\n/* harmony export */   getInvalidTypeRejectionErr: () => (/* binding */ getInvalidTypeRejectionErr),\n/* harmony export */   getTooLargeRejectionErr: () => (/* binding */ getTooLargeRejectionErr),\n/* harmony export */   getTooSmallRejectionErr: () => (/* binding */ getTooSmallRejectionErr),\n/* harmony export */   isAbort: () => (/* binding */ isAbort),\n/* harmony export */   isEvtWithFiles: () => (/* binding */ isEvtWithFiles),\n/* harmony export */   isExt: () => (/* binding */ isExt),\n/* harmony export */   isIeOrEdge: () => (/* binding */ isIeOrEdge),\n/* harmony export */   isKindFile: () => (/* binding */ isKindFile),\n/* harmony export */   isMIMEType: () => (/* binding */ isMIMEType),\n/* harmony export */   isPropagationStopped: () => (/* binding */ isPropagationStopped),\n/* harmony export */   isSecurityError: () => (/* binding */ isSecurityError),\n/* harmony export */   onDocumentDragOver: () => (/* binding */ onDocumentDragOver),\n/* harmony export */   pickerOptionsFromAccept: () => (/* binding */ pickerOptionsFromAccept)\n/* harmony export */ });\n/* harmony import */ var attr_accept__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! attr-accept */ \"(ssr)/./node_modules/attr-accept/dist/es/index.js\");\nfunction _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }\n\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _iterableToArray(iter) { if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter); }\n\nfunction _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\n\nvar accepts = typeof attr_accept__WEBPACK_IMPORTED_MODULE_0__ === \"function\" ? attr_accept__WEBPACK_IMPORTED_MODULE_0__ : attr_accept__WEBPACK_IMPORTED_MODULE_0__[\"default\"]; // Error codes\n\nvar FILE_INVALID_TYPE = \"file-invalid-type\";\nvar FILE_TOO_LARGE = \"file-too-large\";\nvar FILE_TOO_SMALL = \"file-too-small\";\nvar TOO_MANY_FILES = \"too-many-files\";\nvar ErrorCode = {\n  FileInvalidType: FILE_INVALID_TYPE,\n  FileTooLarge: FILE_TOO_LARGE,\n  FileTooSmall: FILE_TOO_SMALL,\n  TooManyFiles: TOO_MANY_FILES\n};\n/**\n *\n * @param {string} accept\n */\n\nvar getInvalidTypeRejectionErr = function getInvalidTypeRejectionErr() {\n  var accept = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : \"\";\n  var acceptArr = accept.split(\",\");\n  var msg = acceptArr.length > 1 ? \"one of \".concat(acceptArr.join(\", \")) : acceptArr[0];\n  return {\n    code: FILE_INVALID_TYPE,\n    message: \"File type must be \".concat(msg)\n  };\n};\nvar getTooLargeRejectionErr = function getTooLargeRejectionErr(maxSize) {\n  return {\n    code: FILE_TOO_LARGE,\n    message: \"File is larger than \".concat(maxSize, \" \").concat(maxSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nvar getTooSmallRejectionErr = function getTooSmallRejectionErr(minSize) {\n  return {\n    code: FILE_TOO_SMALL,\n    message: \"File is smaller than \".concat(minSize, \" \").concat(minSize === 1 ? \"byte\" : \"bytes\")\n  };\n};\nvar TOO_MANY_FILES_REJECTION = {\n  code: TOO_MANY_FILES,\n  message: \"Too many files\"\n};\n/**\n * Check if file is accepted.\n *\n * Firefox versions prior to 53 return a bogus MIME type for every file drag,\n * so dragovers with that MIME type will always be accepted.\n *\n * @param {File} file\n * @param {string} accept\n * @returns\n */\n\nfunction fileAccepted(file, accept) {\n  var isAcceptable = file.type === \"application/x-moz-file\" || accepts(file, accept);\n  return [isAcceptable, isAcceptable ? null : getInvalidTypeRejectionErr(accept)];\n}\nfunction fileMatchSize(file, minSize, maxSize) {\n  if (isDefined(file.size)) {\n    if (isDefined(minSize) && isDefined(maxSize)) {\n      if (file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n      if (file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];\n    } else if (isDefined(minSize) && file.size < minSize) return [false, getTooSmallRejectionErr(minSize)];else if (isDefined(maxSize) && file.size > maxSize) return [false, getTooLargeRejectionErr(maxSize)];\n  }\n\n  return [true, null];\n}\n\nfunction isDefined(value) {\n  return value !== undefined && value !== null;\n}\n/**\n *\n * @param {object} options\n * @param {File[]} options.files\n * @param {string} [options.accept]\n * @param {number} [options.minSize]\n * @param {number} [options.maxSize]\n * @param {boolean} [options.multiple]\n * @param {number} [options.maxFiles]\n * @param {(f: File) => FileError|FileError[]|null} [options.validator]\n * @returns\n */\n\n\nfunction allFilesAccepted(_ref) {\n  var files = _ref.files,\n      accept = _ref.accept,\n      minSize = _ref.minSize,\n      maxSize = _ref.maxSize,\n      multiple = _ref.multiple,\n      maxFiles = _ref.maxFiles,\n      validator = _ref.validator;\n\n  if (!multiple && files.length > 1 || multiple && maxFiles >= 1 && files.length > maxFiles) {\n    return false;\n  }\n\n  return files.every(function (file) {\n    var _fileAccepted = fileAccepted(file, accept),\n        _fileAccepted2 = _slicedToArray(_fileAccepted, 1),\n        accepted = _fileAccepted2[0];\n\n    var _fileMatchSize = fileMatchSize(file, minSize, maxSize),\n        _fileMatchSize2 = _slicedToArray(_fileMatchSize, 1),\n        sizeMatch = _fileMatchSize2[0];\n\n    var customErrors = validator ? validator(file) : null;\n    return accepted && sizeMatch && !customErrors;\n  });\n} // React's synthetic events has event.isPropagationStopped,\n// but to remain compatibility with other libs (Preact) fall back\n// to check event.cancelBubble\n\nfunction isPropagationStopped(event) {\n  if (typeof event.isPropagationStopped === \"function\") {\n    return event.isPropagationStopped();\n  } else if (typeof event.cancelBubble !== \"undefined\") {\n    return event.cancelBubble;\n  }\n\n  return false;\n}\nfunction isEvtWithFiles(event) {\n  if (!event.dataTransfer) {\n    return !!event.target && !!event.target.files;\n  } // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n  // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n\n\n  return Array.prototype.some.call(event.dataTransfer.types, function (type) {\n    return type === \"Files\" || type === \"application/x-moz-file\";\n  });\n}\nfunction isKindFile(item) {\n  return _typeof(item) === \"object\" && item !== null && item.kind === \"file\";\n} // allow the entire document to be a drag target\n\nfunction onDocumentDragOver(event) {\n  event.preventDefault();\n}\n\nfunction isIe(userAgent) {\n  return userAgent.indexOf(\"MSIE\") !== -1 || userAgent.indexOf(\"Trident/\") !== -1;\n}\n\nfunction isEdge(userAgent) {\n  return userAgent.indexOf(\"Edge/\") !== -1;\n}\n\nfunction isIeOrEdge() {\n  var userAgent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : window.navigator.userAgent;\n  return isIe(userAgent) || isEdge(userAgent);\n}\n/**\n * This is intended to be used to compose event handlers\n * They are executed in order until one of them calls `event.isPropagationStopped()`.\n * Note that the check is done on the first invoke too,\n * meaning that if propagation was stopped before invoking the fns,\n * no handlers will be executed.\n *\n * @param {Function} fns the event hanlder functions\n * @return {Function} the event handler to add to an element\n */\n\nfunction composeEventHandlers() {\n  for (var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++) {\n    fns[_key] = arguments[_key];\n  }\n\n  return function (event) {\n    for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n      args[_key2 - 1] = arguments[_key2];\n    }\n\n    return fns.some(function (fn) {\n      if (!isPropagationStopped(event) && fn) {\n        fn.apply(void 0, [event].concat(args));\n      }\n\n      return isPropagationStopped(event);\n    });\n  };\n}\n/**\n * canUseFileSystemAccessAPI checks if the [File System Access API](https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API)\n * is supported by the browser.\n * @returns {boolean}\n */\n\nfunction canUseFileSystemAccessAPI() {\n  return \"showOpenFilePicker\" in window;\n}\n/**\n * Convert the `{accept}` dropzone prop to the\n * `{types}` option for https://developer.mozilla.org/en-US/docs/Web/API/window/showOpenFilePicker\n *\n * @param {AcceptProp} accept\n * @returns {{accept: string[]}[]}\n */\n\nfunction pickerOptionsFromAccept(accept) {\n  if (isDefined(accept)) {\n    var acceptForPicker = Object.entries(accept).filter(function (_ref2) {\n      var _ref3 = _slicedToArray(_ref2, 2),\n          mimeType = _ref3[0],\n          ext = _ref3[1];\n\n      var ok = true;\n\n      if (!isMIMEType(mimeType)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.\"));\n        ok = false;\n      }\n\n      if (!Array.isArray(ext) || !ext.every(isExt)) {\n        console.warn(\"Skipped \\\"\".concat(mimeType, \"\\\" because an invalid file extension was provided.\"));\n        ok = false;\n      }\n\n      return ok;\n    }).reduce(function (agg, _ref4) {\n      var _ref5 = _slicedToArray(_ref4, 2),\n          mimeType = _ref5[0],\n          ext = _ref5[1];\n\n      return _objectSpread(_objectSpread({}, agg), {}, _defineProperty({}, mimeType, ext));\n    }, {});\n    return [{\n      // description is required due to https://crbug.com/1264708\n      description: \"Files\",\n      accept: acceptForPicker\n    }];\n  }\n\n  return accept;\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n * @param {AcceptProp} accept\n * @returns {string}\n */\n\nfunction acceptPropAsAcceptAttr(accept) {\n  if (isDefined(accept)) {\n    return Object.entries(accept).reduce(function (a, _ref6) {\n      var _ref7 = _slicedToArray(_ref6, 2),\n          mimeType = _ref7[0],\n          ext = _ref7[1];\n\n      return [].concat(_toConsumableArray(a), [mimeType], _toConsumableArray(ext));\n    }, []) // Silently discard invalid entries as pickerOptionsFromAccept warns about these\n    .filter(function (v) {\n      return isMIMEType(v) || isExt(v);\n    }).join(\",\");\n  }\n\n  return undefined;\n}\n/**\n * Check if v is an exception caused by aborting a request (e.g window.showOpenFilePicker()).\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is an abort exception.\n */\n\nfunction isAbort(v) {\n  return v instanceof DOMException && (v.name === \"AbortError\" || v.code === v.ABORT_ERR);\n}\n/**\n * Check if v is a security error.\n *\n * See https://developer.mozilla.org/en-US/docs/Web/API/DOMException.\n * @param {any} v\n * @returns {boolean} True if v is a security error.\n */\n\nfunction isSecurityError(v) {\n  return v instanceof DOMException && (v.name === \"SecurityError\" || v.code === v.SECURITY_ERR);\n}\n/**\n * Check if v is a MIME type string.\n *\n * See accepted format: https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input/file#unique_file_type_specifiers.\n *\n * @param {string} v\n */\n\nfunction isMIMEType(v) {\n  return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || v === \"application/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\n/**\n * Check if v is a file extension.\n * @param {string} v\n */\n\nfunction isExt(v) {\n  return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * @typedef {Object.<string, string[]>} AcceptProp\n */\n\n/**\n * @typedef {object} FileError\n * @property {string} message\n * @property {ErrorCode|string} code\n */\n\n/**\n * @typedef {\"file-invalid-type\"|\"file-too-large\"|\"file-too-small\"|\"too-many-files\"} ErrorCode\n *///# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-dropzone/dist/es/utils/index.js\n");

/***/ })

};
;