/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hdHRpJTVDJTVDRG9jdW1lbnRzJTVDJTVDY29kaW5nJTVDJTVDZ3Vlc3MtbXktYWdlJTVDJTVDZ3Vlc3MtbXktYWdlLW5ldyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hdHRpJTVDJTVDRG9jdW1lbnRzJTVDJTVDY29kaW5nJTVDJTVDZ3Vlc3MtbXktYWdlJTVDJTVDZ3Vlc3MtbXktYWdlLW5ldyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2NsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hdHRpJTVDJTVDRG9jdW1lbnRzJTVDJTVDY29kaW5nJTVDJTVDZ3Vlc3MtbXktYWdlJTVDJTVDZ3Vlc3MtbXktYWdlLW5ldyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2Vycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hdHRpJTVDJTVDRG9jdW1lbnRzJTVDJTVDY29kaW5nJTVDJTVDZ3Vlc3MtbXktYWdlJTVDJTVDZ3Vlc3MtbXktYWdlLW5ldyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q2h0dHAtYWNjZXNzLWZhbGxiYWNrJTVDJTVDZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbWF0dGklNUMlNUNEb2N1bWVudHMlNUMlNUNjb2RpbmclNUMlNUNndWVzcy1teS1hZ2UlNUMlNUNndWVzcy1teS1hZ2UtbmV3JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNtYXR0aSU1QyU1Q0RvY3VtZW50cyU1QyU1Q2NvZGluZyU1QyU1Q2d1ZXNzLW15LWFnZSU1QyU1Q2d1ZXNzLW15LWFnZS1uZXclNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNtZXRhZGF0YSU1QyU1Q2FzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hdHRpJTVDJTVDRG9jdW1lbnRzJTVDJTVDY29kaW5nJTVDJTVDZ3Vlc3MtbXktYWdlJTVDJTVDZ3Vlc3MtbXktYWdlLW5ldyU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q21ldGFkYXRhJTVDJTVDbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbWF0dGklNUMlNUNEb2N1bWVudHMlNUMlNUNjb2RpbmclNUMlNUNndWVzcy1teS1hZ2UlNUMlNUNndWVzcy1teS1hZ2UtbmV3JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQXNLO0FBQ3RLO0FBQ0EsME9BQXlLO0FBQ3pLO0FBQ0EsME9BQXlLO0FBQ3pLO0FBQ0Esb1JBQStMO0FBQy9MO0FBQ0Esd09BQXdLO0FBQ3hLO0FBQ0EsNFBBQW1MO0FBQ25MO0FBQ0Esa1FBQXNMO0FBQ3RMO0FBQ0Esc1FBQXVMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYXR0aVxcXFxEb2N1bWVudHNcXFxcY29kaW5nXFxcXGd1ZXNzLW15LWFnZVxcXFxndWVzcy1teS1hZ2UtbmV3XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1hdHRpXFxcXERvY3VtZW50c1xcXFxjb2RpbmdcXFxcZ3Vlc3MtbXktYWdlXFxcXGd1ZXNzLW15LWFnZS1uZXdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWF0dGlcXFxcRG9jdW1lbnRzXFxcXGNvZGluZ1xcXFxndWVzcy1teS1hZ2VcXFxcZ3Vlc3MtbXktYWdlLW5ld1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXGVycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYXR0aVxcXFxEb2N1bWVudHNcXFxcY29kaW5nXFxcXGd1ZXNzLW15LWFnZVxcXFxndWVzcy1teS1hZ2UtbmV3XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcaHR0cC1hY2Nlc3MtZmFsbGJhY2tcXFxcZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1hdHRpXFxcXERvY3VtZW50c1xcXFxjb2RpbmdcXFxcZ3Vlc3MtbXktYWdlXFxcXGd1ZXNzLW15LWFnZS1uZXdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxtYXR0aVxcXFxEb2N1bWVudHNcXFxcY29kaW5nXFxcXGd1ZXNzLW15LWFnZVxcXFxndWVzcy1teS1hZ2UtbmV3XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcbWV0YWRhdGFcXFxcYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1hdHRpXFxcXERvY3VtZW50c1xcXFxjb2RpbmdcXFxcZ3Vlc3MtbXktYWdlXFxcXGd1ZXNzLW15LWFnZS1uZXdcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxtZXRhZGF0YVxcXFxtZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcbWF0dGlcXFxcRG9jdW1lbnRzXFxcXGNvZGluZ1xcXFxndWVzcy1teS1hZ2VcXFxcZ3Vlc3MtbXktYWdlLW5ld1xcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXHJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(rsc)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hdHRpJTVDJTVDRG9jdW1lbnRzJTVDJTVDY29kaW5nJTVDJTVDZ3Vlc3MtbXktYWdlJTVDJTVDZ3Vlc3MtbXktYWdlLW5ldyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1hdHRpXFxcXERvY3VtZW50c1xcXFxjb2RpbmdcXFxcZ3Vlc3MtbXktYWdlXFxcXGd1ZXNzLW15LWFnZS1uZXdcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWF0dGlcXERvY3VtZW50c1xcY29kaW5nXFxndWVzcy1teS1hZ2VcXGd1ZXNzLW15LWFnZS1uZXdcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1hdHRpXFxEb2N1bWVudHNcXGNvZGluZ1xcZ3Vlc3MtbXktYWdlXFxndWVzcy1teS1hZ2UtbmV3XFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MTljYjBmYzNmNjNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Guess My Age - AI Photo Analysis\",\n    description: \"Upload a photo and let AI analyze the person's age or emotions with scientific precision.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\guess-my-age-new\\src\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\guess-my-age-new\\src\\contexts\\AuthContext.tsx",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Documents\\coding\\guess-my-age\\guess-my-age-new\\src\\contexts\\AuthContext.tsx",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Ccontexts%5C%5CAuthContext.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21hdHRpJTVDJTVDRG9jdW1lbnRzJTVDJTVDY29kaW5nJTVDJTVDZ3Vlc3MtbXktYWdlJTVDJTVDZ3Vlc3MtbXktYWdlLW5ldyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBNEgiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1hdHRpXFxcXERvY3VtZW50c1xcXFxjb2RpbmdcXFxcZ3Vlc3MtbXktYWdlXFxcXGd1ZXNzLW15LWFnZS1uZXdcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmatti%5C%5CDocuments%5C%5Ccoding%5C%5Cguess-my-age%5C%5Cguess-my-age-new%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ImageUpload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ImageUpload */ \"(ssr)/./src/components/ImageUpload.tsx\");\n/* harmony import */ var _components_AnalysisSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/AnalysisSelector */ \"(ssr)/./src/components/AnalysisSelector.tsx\");\n/* harmony import */ var _components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/AnalysisResults */ \"(ssr)/./src/components/AnalysisResults.tsx\");\n/* harmony import */ var _components_AuthButton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AuthButton */ \"(ssr)/./src/components/AuthButton.tsx\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/credits */ \"(ssr)/./src/lib/credits.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Sparkles!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction Home() {\n    const { user, userProfile } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageBase64, setImageBase64] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [analysisType, setAnalysisType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('age');\n    const [detailLevel, setDetailLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('low');\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [results, setResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleImageSelect = (file, base64)=>{\n        setSelectedImage(file);\n        setImageBase64(base64);\n        setResults(null);\n        setError('');\n    };\n    const handleRemoveImage = ()=>{\n        setSelectedImage(null);\n        setImageBase64('');\n        setResults(null);\n        setError('');\n    };\n    const handleSelectionChange = (type, level)=>{\n        setAnalysisType(type);\n        setDetailLevel(level);\n        setResults(null);\n        setError('');\n    };\n    const handleAnalyze = async ()=>{\n        if (!imageBase64) {\n            setError('Please select an image first');\n            return;\n        }\n        setIsAnalyzing(true);\n        setError('');\n        setResults(null);\n        try {\n            const response = await fetch('/api/analyze', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    imageBase64,\n                    analysisType,\n                    detailLevel\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || 'Analysis failed');\n            }\n            setResults({\n                result: data.result,\n                analysisType: data.analysisType,\n                isFreeTier: data.isFreeTier\n            });\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An error occurred');\n        } finally{\n            setIsAnalyzing(false);\n        }\n    };\n    const creditCost = (0,_lib_credits__WEBPACK_IMPORTED_MODULE_7__.getCreditCost)(analysisType, detailLevel);\n    const userCredits = userProfile?.credits || 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto px-4 py-4 flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-8 h-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Guess My Age\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthButton__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-4xl mx-auto px-4 py-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl sm:text-4xl font-bold text-gray-900\",\n                                children: \"AI-Powered Photo Analysis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                children: \"Upload a photo and let our AI analyze the person's age or emotions with scientific precision.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImageUpload__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onImageSelect: handleImageSelect,\n                            selectedImage: selectedImage,\n                            onRemoveImage: handleRemoveImage\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg border border-gray-200 p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            onSelectionChange: handleSelectionChange,\n                            selectedType: analysisType,\n                            selectedLevel: detailLevel\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: user ? userCredits >= creditCost ? `This analysis will cost ${creditCost} credit${creditCost > 1 ? 's' : ''}. You have ${userCredits} credits.` : `You need ${creditCost} credit${creditCost > 1 ? 's' : ''} but only have ${userCredits}. Please purchase more credits.` : 'Free analysis for anonymous users (limited features)'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAnalyze,\n                                disabled: isAnalyzing || user && userCredits < creditCost,\n                                className: \"px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2 mx-auto\",\n                                children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Analyzing...\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Sparkles_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"Analyze Photo\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-50 border border-red-200 rounded-lg p-4 text-red-700\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this),\n                    results && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalysisResults__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        result: results.result,\n                        analysisType: results.analysisType,\n                        isFreeTier: results.isFreeTier\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AnalysisResults.tsx":
/*!********************************************!*\
  !*** ./src/components/AnalysisResults.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalysisResults)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Eye,Heart,Lightbulb,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Eye,Heart,Lightbulb,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Eye,Heart,Lightbulb,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Eye,Heart,Lightbulb,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Eye,Heart,Lightbulb,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Eye,Heart,Lightbulb,TrendingUp,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AnalysisResults({ result, analysisType, isFreeTier }) {\n    if (analysisType === 'age') {\n        const ageResult = result;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6 space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                            className: \"w-6 h-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900\",\n                            children: \"Age Analysis Results\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, this),\n                        isFreeTier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n                            children: \"Free Analysis\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-50 rounded-lg p-6 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl font-bold text-blue-600 mb-2\",\n                            children: ageResult.estimatedAge\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg text-gray-700 mb-1\",\n                            children: \"Estimated Age\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-600\",\n                            children: [\n                                \"Range: \",\n                                ageResult.ageRange,\n                                \" years\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            className: \"w-5 h-5 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: \"Confidence\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: [\n                                                ageResult.confidence,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-600 h-2 rounded-full transition-all duration-500\",\n                                        style: {\n                                            width: `${ageResult.confidence}%`\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                ageResult.factors && ageResult.factors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: \"Key Factors\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: ageResult.factors.map((factor, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full\",\n                                    children: factor\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-gray-900\",\n                                    children: \"Scientific Explanation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 leading-relaxed\",\n                            children: ageResult.explanation\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this);\n    }\n    // Emotion Analysis Results\n    const emotionResult = result;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"w-6 h-6 text-pink-600\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"Emotion Analysis Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    isFreeTier && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n                        children: \"Free Analysis\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-pink-50 rounded-lg p-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-3xl font-bold text-pink-600 mb-2 capitalize\",\n                        children: emotionResult.primaryEmotion\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg text-gray-700\",\n                        children: \"Primary Emotion\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-5 h-5 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900\",\n                                children: \"Emotion Breakdown\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: emotionResult.emotions.map((emotion, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 text-sm text-gray-600 capitalize\",\n                                        children: emotion.emotion\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-pink-600 h-2 rounded-full transition-all duration-500\",\n                                                style: {\n                                                    width: `${emotion.confidence}%`\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 text-sm text-gray-600 text-right\",\n                                        children: [\n                                            emotion.confidence,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this),\n            emotionResult.bodyLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-5 h-5 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900\",\n                                children: \"Body Language\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: emotionResult.bodyLanguage\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            emotionResult.context && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900\",\n                                children: \"Context Analysis\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: emotionResult.context\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 157,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Eye_Heart_Lightbulb_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"font-medium text-gray-900\",\n                                children: \"Analysis Explanation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700 leading-relaxed\",\n                        children: emotionResult.explanation\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisResults.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AnalysisResults.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AnalysisSelector.tsx":
/*!*********************************************!*\
  !*** ./src/components/AnalysisSelector.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalysisSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Crown,Heart,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Crown,Heart,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Crown,Heart,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Crown,Heart,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Crown,Heart,Star,Zap!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/credits */ \"(ssr)/./src/lib/credits.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AnalysisSelector({ onSelectionChange, selectedType, selectedLevel }) {\n    const handleTypeChange = (type)=>{\n        const defaultLevel = type === 'age' ? 'low' : 'simple';\n        onSelectionChange(type, defaultLevel);\n    };\n    const handleLevelChange = (level)=>{\n        onSelectionChange(selectedType, level);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                        children: \"Choose Analysis Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleTypeChange('age'),\n                                className: `\n              p-4 rounded-lg border-2 transition-all text-left\n              ${selectedType === 'age' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}\n            `,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            className: `w-6 h-6 ${selectedType === 'age' ? 'text-blue-600' : 'text-gray-400'}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Age Estimation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Estimate the person's age\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 43,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleTypeChange('emotion'),\n                                className: `\n              p-4 rounded-lg border-2 transition-all text-left\n              ${selectedType === 'emotion' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}\n            `,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: `w-6 h-6 ${selectedType === 'emotion' ? 'text-blue-600' : 'text-gray-400'}`\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: \"Emotion Analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Detect emotions and feelings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-3\",\n                        children: selectedType === 'age' ? 'Analysis Detail Level' : 'Analysis Mode'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    selectedType === 'age' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            'low',\n                            'medium',\n                            'high'\n                        ].map((level)=>{\n                            const cost = (0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.getCreditCost)('age', level);\n                            const isSelected = selectedLevel === level;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleLevelChange(level),\n                                className: `\n                    w-full p-4 rounded-lg border-2 transition-all text-left\n                    ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}\n                  `,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                level === 'low' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: `w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 43\n                                                }, this),\n                                                level === 'medium' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: `w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 46\n                                                }, this),\n                                                level === 'high' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: `w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 44\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 capitalize\",\n                                                            children: [\n                                                                level,\n                                                                \" Detail\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                            lineNumber: 99,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                level === 'low' && 'Basic age estimation',\n                                                                level === 'medium' && 'Detailed analysis with factors',\n                                                                level === 'high' && 'Comprehensive analysis with full explanation'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: [\n                                                    cost,\n                                                    \" credit\",\n                                                    cost > 1 ? 's' : ''\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 19\n                                }, this)\n                            }, level, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            'simple',\n                            'advanced'\n                        ].map((level)=>{\n                            const cost = (0,_lib_credits__WEBPACK_IMPORTED_MODULE_1__.getCreditCost)('emotion', level);\n                            const isSelected = selectedLevel === level;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleLevelChange(level),\n                                className: `\n                    w-full p-4 rounded-lg border-2 transition-all text-left\n                    ${isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}\n                  `,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                level === 'simple' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: `w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 135,\n                                                    columnNumber: 46\n                                                }, this),\n                                                level === 'advanced' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Crown_Heart_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: `w-5 h-5 ${isSelected ? 'text-blue-600' : 'text-gray-400'}`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 48\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900 capitalize\",\n                                                            children: [\n                                                                level,\n                                                                \" Analysis\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                level === 'simple' && 'Basic emotion detection',\n                                                                level === 'advanced' && 'Advanced analysis with body language and context'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                            lineNumber: 139,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-gray-900\",\n                                                children: [\n                                                    cost,\n                                                    \" credit\",\n                                                    cost > 1 ? 's' : ''\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 19\n                                }, this)\n                            }, level, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AnalysisSelector.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9BbmFseXNpc1NlbGVjdG9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBR2dFO0FBQytCO0FBUWhGLFNBQVNNLGlCQUFpQixFQUFFQyxpQkFBaUIsRUFBRUMsWUFBWSxFQUFFQyxhQUFhLEVBQXlCO0lBQ2hILE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixNQUFNQyxlQUFlRCxTQUFTLFFBQVEsUUFBUTtRQUM5Q0osa0JBQWtCSSxNQUFNQztJQUMxQjtJQUVBLE1BQU1DLG9CQUFvQixDQUFDQztRQUN6QlAsa0JBQWtCQyxjQUFjTTtJQUNsQztJQUVBLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7O2tDQUNDLDhEQUFDRTt3QkFBR0QsV0FBVTtrQ0FBMkM7Ozs7OztrQ0FDekQsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQ0NDLFNBQVMsSUFBTVQsaUJBQWlCO2dDQUNoQ00sV0FBVyxDQUFDOztjQUVWLEVBQUVSLGlCQUFpQixRQUNmLCtCQUNBLHdDQUNIO1lBQ0gsQ0FBQzswQ0FFRCw0RUFBQ087b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDaEIseUdBQVFBOzRDQUFDZ0IsV0FBVyxDQUFDLFFBQVEsRUFBRVIsaUJBQWlCLFFBQVEsa0JBQWtCLGlCQUFpQjs7Ozs7O3NEQUM1Riw4REFBQ087OzhEQUNDLDhEQUFDSztvREFBR0osV0FBVTs4REFBNEI7Ozs7Ozs4REFDMUMsOERBQUNLO29EQUFFTCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBSzNDLDhEQUFDRTtnQ0FDQ0MsU0FBUyxJQUFNVCxpQkFBaUI7Z0NBQ2hDTSxXQUFXLENBQUM7O2NBRVYsRUFBRVIsaUJBQWlCLFlBQ2YsK0JBQ0Esd0NBQ0g7WUFDSCxDQUFDOzBDQUVELDRFQUFDTztvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNmLHlHQUFLQTs0Q0FBQ2UsV0FBVyxDQUFDLFFBQVEsRUFBRVIsaUJBQWlCLFlBQVksa0JBQWtCLGlCQUFpQjs7Ozs7O3NEQUM3Riw4REFBQ087OzhEQUNDLDhEQUFDSztvREFBR0osV0FBVTs4REFBNEI7Ozs7Ozs4REFDMUMsOERBQUNLO29EQUFFTCxXQUFVOzhEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUS9DLDhEQUFDRDs7a0NBQ0MsOERBQUNFO3dCQUFHRCxXQUFVO2tDQUNYUixpQkFBaUIsUUFBUSwwQkFBMEI7Ozs7OztvQkFHckRBLGlCQUFpQixzQkFDaEIsOERBQUNPO3dCQUFJQyxXQUFVO2tDQUNaOzRCQUFFOzRCQUFPOzRCQUFVO3lCQUFPLENBQXNCTSxHQUFHLENBQUMsQ0FBQ1I7NEJBQ3BELE1BQU1TLE9BQU9sQiwyREFBYUEsQ0FBQyxPQUFPUzs0QkFDbEMsTUFBTVUsYUFBYWYsa0JBQWtCSzs0QkFFckMscUJBQ0UsOERBQUNJO2dDQUVDQyxTQUFTLElBQU1OLGtCQUFrQkM7Z0NBQ2pDRSxXQUFXLENBQUM7O29CQUVWLEVBQUVRLGFBQ0UsK0JBQ0Esd0NBQ0g7a0JBQ0gsQ0FBQzswQ0FFRCw0RUFBQ1Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTs7Z0RBQ1pGLFVBQVUsdUJBQVMsOERBQUNaLHlHQUFHQTtvREFBQ2MsV0FBVyxDQUFDLFFBQVEsRUFBRVEsYUFBYSxrQkFBa0IsaUJBQWlCOzs7Ozs7Z0RBQzlGVixVQUFVLDBCQUFZLDhEQUFDWCx5R0FBSUE7b0RBQUNhLFdBQVcsQ0FBQyxRQUFRLEVBQUVRLGFBQWEsa0JBQWtCLGlCQUFpQjs7Ozs7O2dEQUNsR1YsVUFBVSx3QkFBVSw4REFBQ1YseUdBQUtBO29EQUFDWSxXQUFXLENBQUMsUUFBUSxFQUFFUSxhQUFhLGtCQUFrQixpQkFBaUI7Ozs7Ozs4REFDbEcsOERBQUNUOztzRUFDQyw4REFBQ0s7NERBQUdKLFdBQVU7O2dFQUF3Q0Y7Z0VBQU07Ozs7Ozs7c0VBQzVELDhEQUFDTzs0REFBRUwsV0FBVTs7Z0VBQ1ZGLFVBQVUsU0FBUztnRUFDbkJBLFVBQVUsWUFBWTtnRUFDdEJBLFVBQVUsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJM0IsOERBQUNDOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTs7b0RBQXFDTztvREFBSztvREFBUUEsT0FBTyxJQUFJLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkF6QmpGVDs7Ozs7d0JBOEJYOzs7Ozs2Q0FHRiw4REFBQ0M7d0JBQUlDLFdBQVU7a0NBQ1o7NEJBQUU7NEJBQVU7eUJBQVcsQ0FBMEJNLEdBQUcsQ0FBQyxDQUFDUjs0QkFDckQsTUFBTVMsT0FBT2xCLDJEQUFhQSxDQUFDLFdBQVdTOzRCQUN0QyxNQUFNVSxhQUFhZixrQkFBa0JLOzRCQUVyQyxxQkFDRSw4REFBQ0k7Z0NBRUNDLFNBQVMsSUFBTU4sa0JBQWtCQztnQ0FDakNFLFdBQVcsQ0FBQzs7b0JBRVYsRUFBRVEsYUFDRSwrQkFDQSx3Q0FDSDtrQkFDSCxDQUFDOzBDQUVELDRFQUFDVDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOztnREFDWkYsVUFBVSwwQkFBWSw4REFBQ1oseUdBQUdBO29EQUFDYyxXQUFXLENBQUMsUUFBUSxFQUFFUSxhQUFhLGtCQUFrQixpQkFBaUI7Ozs7OztnREFDakdWLFVBQVUsNEJBQWMsOERBQUNYLHlHQUFJQTtvREFBQ2EsV0FBVyxDQUFDLFFBQVEsRUFBRVEsYUFBYSxrQkFBa0IsaUJBQWlCOzs7Ozs7OERBQ3JHLDhEQUFDVDs7c0VBQ0MsOERBQUNLOzREQUFHSixXQUFVOztnRUFBd0NGO2dFQUFNOzs7Ozs7O3NFQUM1RCw4REFBQ087NERBQUVMLFdBQVU7O2dFQUNWRixVQUFVLFlBQVk7Z0VBQ3RCQSxVQUFVLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSS9CLDhEQUFDQzs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0Q7Z0RBQUlDLFdBQVU7O29EQUFxQ087b0RBQUs7b0RBQVFBLE9BQU8sSUFBSSxNQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7K0JBdkJqRlQ7Ozs7O3dCQTRCWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVoiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcbWF0dGlcXERvY3VtZW50c1xcY29kaW5nXFxndWVzcy1teS1hZ2VcXGd1ZXNzLW15LWFnZS1uZXdcXHNyY1xcY29tcG9uZW50c1xcQW5hbHlzaXNTZWxlY3Rvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBDYWxlbmRhciwgSGVhcnQsIFphcCwgU3RhciwgQ3Jvd24gfSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBBbmFseXNpc1R5cGUsIEFnZURldGFpbExldmVsLCBFbW90aW9uRGV0YWlsTGV2ZWwsIGdldENyZWRpdENvc3QgfSBmcm9tICdAL2xpYi9jcmVkaXRzJ1xuXG5pbnRlcmZhY2UgQW5hbHlzaXNTZWxlY3RvclByb3BzIHtcbiAgb25TZWxlY3Rpb25DaGFuZ2U6ICh0eXBlOiBBbmFseXNpc1R5cGUsIGxldmVsOiBBZ2VEZXRhaWxMZXZlbCB8IEVtb3Rpb25EZXRhaWxMZXZlbCkgPT4gdm9pZFxuICBzZWxlY3RlZFR5cGU6IEFuYWx5c2lzVHlwZVxuICBzZWxlY3RlZExldmVsOiBBZ2VEZXRhaWxMZXZlbCB8IEVtb3Rpb25EZXRhaWxMZXZlbFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBbmFseXNpc1NlbGVjdG9yKHsgb25TZWxlY3Rpb25DaGFuZ2UsIHNlbGVjdGVkVHlwZSwgc2VsZWN0ZWRMZXZlbCB9OiBBbmFseXNpc1NlbGVjdG9yUHJvcHMpIHtcbiAgY29uc3QgaGFuZGxlVHlwZUNoYW5nZSA9ICh0eXBlOiBBbmFseXNpc1R5cGUpID0+IHtcbiAgICBjb25zdCBkZWZhdWx0TGV2ZWwgPSB0eXBlID09PSAnYWdlJyA/ICdsb3cnIDogJ3NpbXBsZSdcbiAgICBvblNlbGVjdGlvbkNoYW5nZSh0eXBlLCBkZWZhdWx0TGV2ZWwpXG4gIH1cblxuICBjb25zdCBoYW5kbGVMZXZlbENoYW5nZSA9IChsZXZlbDogQWdlRGV0YWlsTGV2ZWwgfCBFbW90aW9uRGV0YWlsTGV2ZWwpID0+IHtcbiAgICBvblNlbGVjdGlvbkNoYW5nZShzZWxlY3RlZFR5cGUsIGxldmVsKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgey8qIEFuYWx5c2lzIFR5cGUgU2VsZWN0aW9uICovfVxuICAgICAgPGRpdj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTNcIj5DaG9vc2UgQW5hbHlzaXMgVHlwZTwvaDM+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVR5cGVDaGFuZ2UoJ2FnZScpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgIHAtNCByb3VuZGVkLWxnIGJvcmRlci0yIHRyYW5zaXRpb24tYWxsIHRleHQtbGVmdFxuICAgICAgICAgICAgICAke3NlbGVjdGVkVHlwZSA9PT0gJ2FnZSdcbiAgICAgICAgICAgICAgICA/ICdib3JkZXItYmx1ZS01MDAgYmctYmx1ZS01MCdcbiAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICBgfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT17YHctNiBoLTYgJHtzZWxlY3RlZFR5cGUgPT09ICdhZ2UnID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS00MDAnfWB9IC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5BZ2UgRXN0aW1hdGlvbjwvaDQ+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+RXN0aW1hdGUgdGhlIHBlcnNvbidzIGFnZTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVR5cGVDaGFuZ2UoJ2Vtb3Rpb24nKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YFxuICAgICAgICAgICAgICBwLTQgcm91bmRlZC1sZyBib3JkZXItMiB0cmFuc2l0aW9uLWFsbCB0ZXh0LWxlZnRcbiAgICAgICAgICAgICAgJHtzZWxlY3RlZFR5cGUgPT09ICdlbW90aW9uJ1xuICAgICAgICAgICAgICAgID8gJ2JvcmRlci1ibHVlLTUwMCBiZy1ibHVlLTUwJ1xuICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGB9XG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICA8SGVhcnQgY2xhc3NOYW1lPXtgdy02IGgtNiAke3NlbGVjdGVkVHlwZSA9PT0gJ2Vtb3Rpb24nID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS00MDAnfWB9IC8+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj5FbW90aW9uIEFuYWx5c2lzPC9oND5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5EZXRlY3QgZW1vdGlvbnMgYW5kIGZlZWxpbmdzPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRGV0YWlsIExldmVsIFNlbGVjdGlvbiAqL31cbiAgICAgIDxkaXY+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0zXCI+XG4gICAgICAgICAge3NlbGVjdGVkVHlwZSA9PT0gJ2FnZScgPyAnQW5hbHlzaXMgRGV0YWlsIExldmVsJyA6ICdBbmFseXNpcyBNb2RlJ31cbiAgICAgICAgPC9oMz5cbiAgICAgICAgXG4gICAgICAgIHtzZWxlY3RlZFR5cGUgPT09ICdhZ2UnID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgICAgICB7KFsnbG93JywgJ21lZGl1bScsICdoaWdoJ10gYXMgQWdlRGV0YWlsTGV2ZWxbXSkubWFwKChsZXZlbCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBjb3N0ID0gZ2V0Q3JlZGl0Q29zdCgnYWdlJywgbGV2ZWwpXG4gICAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZExldmVsID09PSBsZXZlbFxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2xldmVsfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTGV2ZWxDaGFuZ2UobGV2ZWwpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgICAgICAgIHctZnVsbCBwLTQgcm91bmRlZC1sZyBib3JkZXItMiB0cmFuc2l0aW9uLWFsbCB0ZXh0LWxlZnRcbiAgICAgICAgICAgICAgICAgICAgJHtpc1NlbGVjdGVkXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIGJnLWJsdWUtNTAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMjAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgYH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2xldmVsID09PSAnbG93JyAmJiA8WmFwIGNsYXNzTmFtZT17YHctNSBoLTUgJHtpc1NlbGVjdGVkID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS00MDAnfWB9IC8+fVxuICAgICAgICAgICAgICAgICAgICAgIHtsZXZlbCA9PT0gJ21lZGl1bScgJiYgPFN0YXIgY2xhc3NOYW1lPXtgdy01IGgtNSAke2lzU2VsZWN0ZWQgPyAndGV4dC1ibHVlLTYwMCcgOiAndGV4dC1ncmF5LTQwMCd9YH0gLz59XG4gICAgICAgICAgICAgICAgICAgICAge2xldmVsID09PSAnaGlnaCcgJiYgPENyb3duIGNsYXNzTmFtZT17YHctNSBoLTUgJHtpc1NlbGVjdGVkID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS00MDAnfWB9IC8+fVxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMCBjYXBpdGFsaXplXCI+e2xldmVsfSBEZXRhaWw8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsZXZlbCA9PT0gJ2xvdycgJiYgJ0Jhc2ljIGFnZSBlc3RpbWF0aW9uJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xldmVsID09PSAnbWVkaXVtJyAmJiAnRGV0YWlsZWQgYW5hbHlzaXMgd2l0aCBmYWN0b3JzJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xldmVsID09PSAnaGlnaCcgJiYgJ0NvbXByZWhlbnNpdmUgYW5hbHlzaXMgd2l0aCBmdWxsIGV4cGxhbmF0aW9uJ31cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwXCI+e2Nvc3R9IGNyZWRpdHtjb3N0ID4gMSA/ICdzJyA6ICcnfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICApXG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktM1wiPlxuICAgICAgICAgICAgeyhbJ3NpbXBsZScsICdhZHZhbmNlZCddIGFzIEVtb3Rpb25EZXRhaWxMZXZlbFtdKS5tYXAoKGxldmVsKSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IGNvc3QgPSBnZXRDcmVkaXRDb3N0KCdlbW90aW9uJywgbGV2ZWwpXG4gICAgICAgICAgICAgIGNvbnN0IGlzU2VsZWN0ZWQgPSBzZWxlY3RlZExldmVsID09PSBsZXZlbFxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2xldmVsfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gaGFuZGxlTGV2ZWxDaGFuZ2UobGV2ZWwpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgICAgICAgIHctZnVsbCBwLTQgcm91bmRlZC1sZyBib3JkZXItMiB0cmFuc2l0aW9uLWFsbCB0ZXh0LWxlZnRcbiAgICAgICAgICAgICAgICAgICAgJHtpc1NlbGVjdGVkXG4gICAgICAgICAgICAgICAgICAgICAgPyAnYm9yZGVyLWJsdWUtNTAwIGJnLWJsdWUtNTAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYm9yZGVyLWdyYXktMjAwIGhvdmVyOmJvcmRlci1ncmF5LTMwMCdcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgYH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2xldmVsID09PSAnc2ltcGxlJyAmJiA8WmFwIGNsYXNzTmFtZT17YHctNSBoLTUgJHtpc1NlbGVjdGVkID8gJ3RleHQtYmx1ZS02MDAnIDogJ3RleHQtZ3JheS00MDAnfWB9IC8+fVxuICAgICAgICAgICAgICAgICAgICAgIHtsZXZlbCA9PT0gJ2FkdmFuY2VkJyAmJiA8U3RhciBjbGFzc05hbWU9e2B3LTUgaC01ICR7aXNTZWxlY3RlZCA/ICd0ZXh0LWJsdWUtNjAwJyA6ICd0ZXh0LWdyYXktNDAwJ31gfSAvPn1cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDAgY2FwaXRhbGl6ZVwiPntsZXZlbH0gQW5hbHlzaXM8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtsZXZlbCA9PT0gJ3NpbXBsZScgJiYgJ0Jhc2ljIGVtb3Rpb24gZGV0ZWN0aW9uJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAge2xldmVsID09PSAnYWR2YW5jZWQnICYmICdBZHZhbmNlZCBhbmFseXNpcyB3aXRoIGJvZHkgbGFuZ3VhZ2UgYW5kIGNvbnRleHQnfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS05MDBcIj57Y29zdH0gY3JlZGl0e2Nvc3QgPiAxID8gJ3MnIDogJyd9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIClcbiAgICAgICAgICAgIH0pfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJDYWxlbmRhciIsIkhlYXJ0IiwiWmFwIiwiU3RhciIsIkNyb3duIiwiZ2V0Q3JlZGl0Q29zdCIsIkFuYWx5c2lzU2VsZWN0b3IiLCJvblNlbGVjdGlvbkNoYW5nZSIsInNlbGVjdGVkVHlwZSIsInNlbGVjdGVkTGV2ZWwiLCJoYW5kbGVUeXBlQ2hhbmdlIiwidHlwZSIsImRlZmF1bHRMZXZlbCIsImhhbmRsZUxldmVsQ2hhbmdlIiwibGV2ZWwiLCJkaXYiLCJjbGFzc05hbWUiLCJoMyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJoNCIsInAiLCJtYXAiLCJjb3N0IiwiaXNTZWxlY3RlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AnalysisSelector.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthButton.tsx":
/*!***************************************!*\
  !*** ./src/components/AuthButton.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _AuthModal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./AuthModal */ \"(ssr)/./src/components/AuthModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AuthButton() {\n    const { user, userProfile, signOut, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-200 h-10 w-24 rounded-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this);\n    }\n    if (user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden sm:flex items-center gap-2 text-sm text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                userProfile?.credits || 0,\n                                \" credits\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: signOut,\n                    className: \"flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            size: 20\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"hidden sm:inline\",\n                            children: \"Sign Out\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowAuthModal(true),\n                className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Sign In\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AuthModal__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isOpen: showAuthModal,\n                onClose: ()=>setShowAuthModal(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthButton.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AuthModal.tsx":
/*!**************************************!*\
  !*** ./src/components/AuthModal.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lock_Mail_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Lock,Mail,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Lock_Mail_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Lock,Mail,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Lock_Mail_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Lock,Mail,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AuthModal({ isOpen, onClose, initialMode = 'login' }) {\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialMode);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { signIn, signUp } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n        try {\n            const { error } = mode === 'login' ? await signIn(email, password) : await signUp(email, password);\n            if (error) {\n                setError(error.message);\n            } else {\n                if (mode === 'signup') {\n                    setError('Check your email for the confirmation link!');\n                } else {\n                    onClose();\n                }\n            }\n        } catch (err) {\n            setError('An unexpected error occurred');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetForm = ()=>{\n        setEmail('');\n        setPassword('');\n        setError('');\n    };\n    const switchMode = ()=>{\n        setMode(mode === 'login' ? 'signup' : 'login');\n        resetForm();\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-md w-full p-6 relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: onClose,\n                    className: \"absolute top-4 right-4 text-gray-400 hover:text-gray-600\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lock_Mail_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        size: 24\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: mode === 'login' ? 'Welcome Back' : 'Create Account'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: mode === 'login' ? 'Sign in to your account to continue' : 'Sign up to start analyzing photos'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Email\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lock_Mail_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            value: email,\n                                            onChange: (e)=>setEmail(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"Enter your email\",\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: \"Password\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lock_Mail_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400\",\n                                            size: 20\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"password\",\n                                            value: password,\n                                            onChange: (e)=>setPassword(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                            placeholder: \"Enter your password\",\n                                            required: true,\n                                            minLength: 6\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-red-600 text-sm bg-red-50 p-3 rounded-lg\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: loading,\n                            className: \"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                            children: loading ? 'Loading...' : mode === 'login' ? 'Sign In' : 'Sign Up'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            mode === 'login' ? \"Don't have an account?\" : \"Already have an account?\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: switchMode,\n                                className: \"ml-1 text-blue-600 hover:text-blue-700 font-medium\",\n                                children: mode === 'login' ? 'Sign up' : 'Sign in'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\AuthModal.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AuthModal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ImageUpload.tsx":
/*!****************************************!*\
  !*** ./src/components/ImageUpload.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageUpload)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_dropzone__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dropzone */ \"(ssr)/./node_modules/react-dropzone/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Image,Upload,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ImageUpload({ onImageSelect, selectedImage, onRemoveImage }) {\n    const [preview, setPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const onDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ImageUpload.useCallback[onDrop]\": (acceptedFiles)=>{\n            const file = acceptedFiles[0];\n            if (file) {\n                // Validate file size (max 10MB)\n                if (file.size > 10 * 1024 * 1024) {\n                    alert('File size must be less than 10MB');\n                    return;\n                }\n                // Validate file type\n                if (!file.type.startsWith('image/')) {\n                    alert('Please select an image file');\n                    return;\n                }\n                // Create preview\n                const reader = new FileReader();\n                reader.onload = ({\n                    \"ImageUpload.useCallback[onDrop]\": (e)=>{\n                        const result = e.target?.result;\n                        setPreview(result);\n                        onImageSelect(file, result);\n                    }\n                })[\"ImageUpload.useCallback[onDrop]\"];\n                reader.readAsDataURL(file);\n            }\n        }\n    }[\"ImageUpload.useCallback[onDrop]\"], [\n        onImageSelect\n    ]);\n    const { getRootProps, getInputProps, isDragActive } = (0,react_dropzone__WEBPACK_IMPORTED_MODULE_2__.useDropzone)({\n        onDrop,\n        accept: {\n            'image/*': [\n                '.jpeg',\n                '.jpg',\n                '.png',\n                '.webp',\n                '.gif'\n            ]\n        },\n        multiple: false,\n        maxSize: 10 * 1024 * 1024\n    });\n    const handleRemove = ()=>{\n        setPreview(null);\n        onRemoveImage();\n    };\n    if (selectedImage && preview) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative bg-gray-50 rounded-lg overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: preview,\n                            alt: \"Selected image\",\n                            className: \"w-full h-64 object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRemove,\n                            className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                size: 16\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2 text-sm text-gray-600 text-center\",\n                    children: [\n                        selectedImage.name,\n                        \" (\",\n                        (selectedImage.size / 1024 / 1024).toFixed(2),\n                        \" MB)\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ...getRootProps(),\n        className: `\n        border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n        ${isDragActive ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-gray-400'}\n      `,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                ...getInputProps()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `\n          p-4 rounded-full \n          ${isDragActive ? 'bg-blue-100' : 'bg-gray-100'}\n        `,\n                        children: isDragActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-8 h-8 text-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Image_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-8 h-8 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-medium text-gray-900\",\n                                children: isDragActive ? 'Drop your image here' : 'Upload a photo'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 mt-1\",\n                                children: \"Drag and drop or click to select\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-400 mt-2\",\n                                children: \"Supports: JPEG, PNG, WebP, GIF (max 10MB)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\components\\\\ImageUpload.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ImageUpload.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchUserProfile = async (userId)=>{\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('users').select('*').eq('id', userId).single();\n            if (error) {\n                console.error('Error fetching user profile:', error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error('Error fetching user profile:', error);\n            return null;\n        }\n    };\n    const refreshUserProfile = async ()=>{\n        if (user) {\n            const profile = await fetchUserProfile(user.id);\n            setUserProfile(profile);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session } })=>{\n                    setUser(session?.user ?? null);\n                    if (session?.user) {\n                        fetchUserProfile(session.user.id).then(setUserProfile);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Listen for auth changes\n            const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    setUser(session?.user ?? null);\n                    if (session?.user) {\n                        const profile = await fetchUserProfile(session.user.id);\n                        setUserProfile(profile);\n                    } else {\n                        setUserProfile(null);\n                    }\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signIn = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password)=>{\n        const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n    };\n    const value = {\n        user,\n        userProfile,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        refreshUserProfile\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/credits.ts":
/*!****************************!*\
  !*** ./src/lib/credits.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CREDIT_COSTS: () => (/* binding */ CREDIT_COSTS),\n/* harmony export */   DAILY_FREE_LIMITS: () => (/* binding */ DAILY_FREE_LIMITS),\n/* harmony export */   addCredits: () => (/* binding */ addCredits),\n/* harmony export */   checkUserCanAnalyze: () => (/* binding */ checkUserCanAnalyze),\n/* harmony export */   deductCredits: () => (/* binding */ deductCredits),\n/* harmony export */   getCreditCost: () => (/* binding */ getCreditCost)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/./src/lib/supabase.ts\");\n\nconst CREDIT_COSTS = {\n    age: {\n        low: 1,\n        medium: 2,\n        high: 3\n    },\n    emotion: {\n        simple: 1,\n        advanced: 2\n    }\n};\nconst DAILY_FREE_LIMITS = {\n    anonymous: 1,\n    registered: 3\n};\nfunction getCreditCost(analysisType, detailLevel) {\n    if (analysisType === 'age') {\n        return CREDIT_COSTS.age[detailLevel];\n    } else {\n        return CREDIT_COSTS.emotion[detailLevel];\n    }\n}\nasync function checkUserCanAnalyze(userId, analysisType, detailLevel) {\n    const creditCost = getCreditCost(analysisType, detailLevel);\n    // Anonymous user\n    if (!userId) {\n        // Check if anonymous user has used their daily free analysis\n        const today = new Date().toISOString().split('T')[0];\n        const anonymousUsageKey = `anonymous_usage_${today}`;\n        // In a real app, you'd store this in localStorage or a session\n        // For now, we'll allow 1 free use per session for anonymous users\n        return {\n            canAnalyze: true,\n            isFreeTier: true\n        };\n    }\n    // Registered user\n    try {\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').select('credits, daily_free_uses, last_free_use_date').eq('id', userId).single();\n        if (error || !user) {\n            return {\n                canAnalyze: false,\n                reason: 'User not found',\n                isFreeTier: false\n            };\n        }\n        const today = new Date().toISOString().split('T')[0];\n        const lastFreeUseDate = user.last_free_use_date;\n        // Reset daily free uses if it's a new day\n        let dailyFreeUses = user.daily_free_uses;\n        if (lastFreeUseDate !== today) {\n            dailyFreeUses = 0;\n        }\n        // Check if user can use free tier\n        if (dailyFreeUses < DAILY_FREE_LIMITS.registered && creditCost === 1) {\n            return {\n                canAnalyze: true,\n                isFreeTier: true\n            };\n        }\n        // Check if user has enough credits\n        if (user.credits >= creditCost) {\n            return {\n                canAnalyze: true,\n                isFreeTier: false\n            };\n        }\n        return {\n            canAnalyze: false,\n            reason: `Not enough credits. Need ${creditCost}, have ${user.credits}`,\n            isFreeTier: false\n        };\n    } catch (error) {\n        console.error('Error checking user credits:', error);\n        return {\n            canAnalyze: false,\n            reason: 'Error checking credits',\n            isFreeTier: false\n        };\n    }\n}\nasync function deductCredits(userId, analysisType, detailLevel, isFreeTier, resultData) {\n    const creditCost = getCreditCost(analysisType, detailLevel);\n    try {\n        const supabaseServer = (0,_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n        if (isFreeTier) {\n            // Update daily free uses\n            const today = new Date().toISOString().split('T')[0];\n            const { error: updateError } = await supabaseServer.from('users').update({\n                daily_free_uses: supabaseServer.raw('daily_free_uses + 1'),\n                last_free_use_date: today\n            }).eq('id', userId);\n            if (updateError) {\n                console.error('Error updating daily free uses:', updateError);\n                return {\n                    success: false,\n                    error: 'Failed to update usage'\n                };\n            }\n        } else {\n            // Deduct credits\n            const { error: updateError } = await supabaseServer.from('users').update({\n                credits: supabaseServer.raw(`credits - ${creditCost}`)\n            }).eq('id', userId);\n            if (updateError) {\n                console.error('Error deducting credits:', updateError);\n                return {\n                    success: false,\n                    error: 'Failed to deduct credits'\n                };\n            }\n        }\n        // Log the usage\n        const { error: logError } = await supabaseServer.from('usage_logs').insert({\n            user_id: userId,\n            analysis_type: analysisType,\n            detail_level: detailLevel,\n            credits_used: isFreeTier ? 0 : creditCost,\n            is_free_use: isFreeTier,\n            result_data: resultData\n        });\n        if (logError) {\n            console.error('Error logging usage:', logError);\n        // Don't fail the operation if logging fails\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error in deductCredits:', error);\n        return {\n            success: false,\n            error: 'Unexpected error'\n        };\n    }\n}\nasync function addCredits(userId, credits, transactionId) {\n    try {\n        const supabaseServer = (0,_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n        const { error } = await supabaseServer.from('users').update({\n            credits: supabaseServer.raw(`credits + ${credits}`)\n        }).eq('id', userId);\n        if (error) {\n            console.error('Error adding credits:', error);\n            return {\n                success: false,\n                error: 'Failed to add credits'\n            };\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error in addCredits:', error);\n        return {\n            success: false,\n            error: 'Unexpected error'\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/credits.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n\nconst supabaseUrl = \"https://gsuvqpwagpdwwcmtggyy.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY\";\n// Client-side Supabase client\nconst supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client (for API routes)\nconst createServerClient = ()=>{\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/prop-types","vendor-chunks/file-selector","vendor-chunks/react-dropzone","vendor-chunks/react-is","vendor-chunks/tslib","vendor-chunks/object-assign","vendor-chunks/attr-accept"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();