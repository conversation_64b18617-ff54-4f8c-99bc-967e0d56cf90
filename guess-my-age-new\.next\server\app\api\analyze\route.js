/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analyze/route";
exports.ids = ["app/api/analyze/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_matti_Documents_coding_guess_my_age_guess_my_age_new_src_app_api_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analyze/route.ts */ \"(rsc)/./src/app/api/analyze/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analyze/route\",\n        pathname: \"/api/analyze\",\n        filename: \"route\",\n        bundlePath: \"app/api/analyze/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\coding\\\\guess-my-age\\\\guess-my-age-new\\\\src\\\\app\\\\api\\\\analyze\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_matti_Documents_coding_guess_my_age_guess_my_age_new_src_app_api_analyze_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analyze/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/analyze/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_credits__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/credits */ \"(rsc)/./src/lib/credits.ts\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        const { imageBase64, analysisType, detailLevel } = await request.json();\n        if (!imageBase64 || !analysisType || !detailLevel) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required fields'\n            }, {\n                status: 400\n            });\n        }\n        // Get user from session\n        const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.createServerClient)();\n        const { data: { session } } = await supabase.auth.getSession();\n        const userId = session?.user?.id || null;\n        // Check if user can perform analysis\n        const { canAnalyze, reason, isFreeTier } = await (0,_lib_credits__WEBPACK_IMPORTED_MODULE_2__.checkUserCanAnalyze)(userId, analysisType, detailLevel);\n        if (!canAnalyze) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: reason || 'Cannot perform analysis'\n            }, {\n                status: 403\n            });\n        }\n        // Perform analysis\n        let result;\n        try {\n            if (analysisType === 'age') {\n                result = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_3__.analyzeAge)(imageBase64, detailLevel);\n            } else if (analysisType === 'emotion') {\n                result = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_3__.analyzeEmotion)(imageBase64, detailLevel);\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Invalid analysis type'\n                }, {\n                    status: 400\n                });\n            }\n        } catch (error) {\n            console.error('Analysis error:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to analyze image'\n            }, {\n                status: 500\n            });\n        }\n        // Deduct credits if user is registered\n        if (userId) {\n            const { success, error } = await (0,_lib_credits__WEBPACK_IMPORTED_MODULE_2__.deductCredits)(userId, analysisType, detailLevel, isFreeTier, result);\n            if (!success) {\n                console.error('Failed to deduct credits:', error);\n            // Don't fail the request, but log the error\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            result,\n            isFreeTier,\n            analysisType,\n            detailLevel\n        });\n    } catch (error) {\n        console.error('API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analyze/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/credits.ts":
/*!****************************!*\
  !*** ./src/lib/credits.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CREDIT_COSTS: () => (/* binding */ CREDIT_COSTS),\n/* harmony export */   DAILY_FREE_LIMITS: () => (/* binding */ DAILY_FREE_LIMITS),\n/* harmony export */   addCredits: () => (/* binding */ addCredits),\n/* harmony export */   checkUserCanAnalyze: () => (/* binding */ checkUserCanAnalyze),\n/* harmony export */   deductCredits: () => (/* binding */ deductCredits),\n/* harmony export */   getCreditCost: () => (/* binding */ getCreditCost)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\nconst CREDIT_COSTS = {\n    age: {\n        low: 1,\n        medium: 2,\n        high: 3\n    },\n    emotion: {\n        simple: 1,\n        advanced: 2\n    }\n};\nconst DAILY_FREE_LIMITS = {\n    anonymous: 1,\n    registered: 3\n};\nfunction getCreditCost(analysisType, detailLevel) {\n    if (analysisType === 'age') {\n        return CREDIT_COSTS.age[detailLevel];\n    } else {\n        return CREDIT_COSTS.emotion[detailLevel];\n    }\n}\nasync function checkUserCanAnalyze(userId, analysisType, detailLevel) {\n    const creditCost = getCreditCost(analysisType, detailLevel);\n    // Anonymous user\n    if (!userId) {\n        // Check if anonymous user has used their daily free analysis\n        const today = new Date().toISOString().split('T')[0];\n        const anonymousUsageKey = `anonymous_usage_${today}`;\n        // In a real app, you'd store this in localStorage or a session\n        // For now, we'll allow 1 free use per session for anonymous users\n        return {\n            canAnalyze: true,\n            isFreeTier: true\n        };\n    }\n    // Registered user\n    try {\n        const { data: user, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('users').select('credits, daily_free_uses, last_free_use_date').eq('id', userId).single();\n        if (error || !user) {\n            return {\n                canAnalyze: false,\n                reason: 'User not found',\n                isFreeTier: false\n            };\n        }\n        const today = new Date().toISOString().split('T')[0];\n        const lastFreeUseDate = user.last_free_use_date;\n        // Reset daily free uses if it's a new day\n        let dailyFreeUses = user.daily_free_uses;\n        if (lastFreeUseDate !== today) {\n            dailyFreeUses = 0;\n        }\n        // Check if user can use free tier\n        if (dailyFreeUses < DAILY_FREE_LIMITS.registered && creditCost === 1) {\n            return {\n                canAnalyze: true,\n                isFreeTier: true\n            };\n        }\n        // Check if user has enough credits\n        if (user.credits >= creditCost) {\n            return {\n                canAnalyze: true,\n                isFreeTier: false\n            };\n        }\n        return {\n            canAnalyze: false,\n            reason: `Not enough credits. Need ${creditCost}, have ${user.credits}`,\n            isFreeTier: false\n        };\n    } catch (error) {\n        console.error('Error checking user credits:', error);\n        return {\n            canAnalyze: false,\n            reason: 'Error checking credits',\n            isFreeTier: false\n        };\n    }\n}\nasync function deductCredits(userId, analysisType, detailLevel, isFreeTier, resultData) {\n    const creditCost = getCreditCost(analysisType, detailLevel);\n    try {\n        const supabaseServer = (0,_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n        if (isFreeTier) {\n            // Update daily free uses\n            const today = new Date().toISOString().split('T')[0];\n            const { error: updateError } = await supabaseServer.from('users').update({\n                daily_free_uses: supabaseServer.raw('daily_free_uses + 1'),\n                last_free_use_date: today\n            }).eq('id', userId);\n            if (updateError) {\n                console.error('Error updating daily free uses:', updateError);\n                return {\n                    success: false,\n                    error: 'Failed to update usage'\n                };\n            }\n        } else {\n            // Deduct credits\n            const { error: updateError } = await supabaseServer.from('users').update({\n                credits: supabaseServer.raw(`credits - ${creditCost}`)\n            }).eq('id', userId);\n            if (updateError) {\n                console.error('Error deducting credits:', updateError);\n                return {\n                    success: false,\n                    error: 'Failed to deduct credits'\n                };\n            }\n        }\n        // Log the usage\n        const { error: logError } = await supabaseServer.from('usage_logs').insert({\n            user_id: userId,\n            analysis_type: analysisType,\n            detail_level: detailLevel,\n            credits_used: isFreeTier ? 0 : creditCost,\n            is_free_use: isFreeTier,\n            result_data: resultData\n        });\n        if (logError) {\n            console.error('Error logging usage:', logError);\n        // Don't fail the operation if logging fails\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error in deductCredits:', error);\n        return {\n            success: false,\n            error: 'Unexpected error'\n        };\n    }\n}\nasync function addCredits(userId, credits, transactionId) {\n    try {\n        const supabaseServer = (0,_supabase__WEBPACK_IMPORTED_MODULE_0__.createServerClient)();\n        const { error } = await supabaseServer.from('users').update({\n            credits: supabaseServer.raw(`credits + ${credits}`)\n        }).eq('id', userId);\n        if (error) {\n            console.error('Error adding credits:', error);\n            return {\n                success: false,\n                error: 'Failed to add credits'\n            };\n        }\n        return {\n            success: true\n        };\n    } catch (error) {\n        console.error('Error in addCredits:', error);\n        return {\n            success: false,\n            error: 'Unexpected error'\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/credits.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeAge: () => (/* binding */ analyzeAge),\n/* harmony export */   analyzeEmotion: () => (/* binding */ analyzeEmotion)\n/* harmony export */ });\nconst OPENAI_API_KEY = process.env.OPENAI_API_KEY;\nconst AGE_PROMPTS = {\n    low: `Analyze this photo and estimate the person's age. Provide:\n1. Estimated age (single number)\n2. Age range (e.g., \"25-30\")\n3. Confidence level (0-100)\n4. Brief explanation (2-3 sentences)\n\nFocus on basic facial features like skin texture, wrinkles, and overall appearance.`,\n    medium: `Analyze this photo and estimate the person's age with moderate detail. Provide:\n1. Estimated age (single number)\n2. Age range (e.g., \"25-30\")\n3. Confidence level (0-100)\n4. Detailed explanation (4-5 sentences)\n5. Key factors that influenced your assessment\n\nConsider facial features, skin condition, hair, eyes, and overall facial structure.`,\n    high: `Analyze this photo and estimate the person's age with high precision. Provide:\n1. Estimated age (single number)\n2. Age range (e.g., \"25-30\")\n3. Confidence level (0-100)\n4. Comprehensive explanation (6-8 sentences)\n5. Detailed list of factors that influenced your assessment\n6. Discussion of any challenging aspects or uncertainties\n\nPerform a thorough analysis considering: facial skin texture and elasticity, wrinkle patterns, eye area characteristics, jawline definition, hair condition, neck appearance, hand visibility (if present), overall facial proportions, and any other age-related indicators.`\n};\nconst EMOTION_PROMPTS = {\n    simple: `Analyze the emotions shown in this photo. Provide:\n1. Primary emotion\n2. List of detected emotions with confidence levels\n3. Brief explanation (2-3 sentences)\n\nFocus on basic facial expressions and obvious emotional indicators.`,\n    advanced: `Perform an advanced emotional analysis of this photo. Provide:\n1. Primary emotion\n2. Detailed list of detected emotions with confidence levels\n3. Comprehensive explanation (4-6 sentences)\n4. Body language analysis (if visible)\n5. Contextual emotional indicators\n\nConsider micro-expressions, eye contact, posture, hand gestures, environmental context, and subtle emotional cues that might not be immediately obvious.`\n};\nasync function analyzeAge(imageBase64, detailLevel) {\n    try {\n        const response = await fetch('https://api.openai.com/v1/responses', {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${OPENAI_API_KEY}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: 'gpt-4.1-mini',\n                input: [\n                    {\n                        role: 'user',\n                        content: [\n                            {\n                                type: 'input_text',\n                                text: AGE_PROMPTS[detailLevel]\n                            },\n                            {\n                                type: 'input_image',\n                                image_url: imageBase64,\n                                detail: detailLevel === 'low' ? 'low' : 'high'\n                            }\n                        ]\n                    }\n                ]\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`OpenAI API error: ${response.status}`);\n        }\n        const data = await response.json();\n        const content = data.output_text;\n        // Parse the response to extract structured data\n        const result = parseAgeAnalysis(content);\n        return result;\n    } catch (error) {\n        console.error('Error analyzing age:', error);\n        throw new Error('Failed to analyze age');\n    }\n}\nasync function analyzeEmotion(imageBase64, detailLevel) {\n    try {\n        const response = await fetch('https://api.openai.com/v1/responses', {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${OPENAI_API_KEY}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                model: 'gpt-4.1-mini',\n                input: [\n                    {\n                        role: 'user',\n                        content: [\n                            {\n                                type: 'input_text',\n                                text: EMOTION_PROMPTS[detailLevel]\n                            },\n                            {\n                                type: 'input_image',\n                                image_url: imageBase64,\n                                detail: detailLevel === 'simple' ? 'low' : 'high'\n                            }\n                        ]\n                    }\n                ]\n            })\n        });\n        if (!response.ok) {\n            throw new Error(`OpenAI API error: ${response.status}`);\n        }\n        const data = await response.json();\n        const content = data.output_text;\n        // Parse the response to extract structured data\n        const result = parseEmotionAnalysis(content, detailLevel);\n        return result;\n    } catch (error) {\n        console.error('Error analyzing emotion:', error);\n        throw new Error('Failed to analyze emotion');\n    }\n}\nfunction parseAgeAnalysis(content) {\n    // Simple parsing - in a real app, you might want more sophisticated parsing\n    const ageMatch = content.match(/(?:age|estimated)[:\\s]*(\\d+)/i);\n    const rangeMatch = content.match(/(?:range|between)[:\\s]*(\\d+[-–]\\d+)/i);\n    const confidenceMatch = content.match(/confidence[:\\s]*(\\d+)/i);\n    return {\n        estimatedAge: ageMatch ? parseInt(ageMatch[1]) : 25,\n        ageRange: rangeMatch ? rangeMatch[1] : '20-30',\n        confidence: confidenceMatch ? parseInt(confidenceMatch[1]) : 75,\n        explanation: content,\n        factors: extractFactors(content)\n    };\n}\nfunction parseEmotionAnalysis(content, detailLevel) {\n    // Extract primary emotion\n    const emotionMatch = content.match(/(?:primary|main|dominant)\\s+emotion[:\\s]*(\\w+)/i);\n    const primaryEmotion = emotionMatch ? emotionMatch[1] : 'neutral';\n    // Extract emotions list (simplified)\n    const emotions = [\n        {\n            emotion: primaryEmotion,\n            confidence: 85\n        },\n        {\n            emotion: 'neutral',\n            confidence: 15\n        }\n    ];\n    const result = {\n        primaryEmotion,\n        emotions,\n        explanation: content\n    };\n    if (detailLevel === 'advanced') {\n        result.bodyLanguage = 'Body language analysis included in explanation';\n        result.context = 'Contextual analysis included in explanation';\n    }\n    return result;\n}\nfunction extractFactors(content) {\n    // Simple factor extraction - could be improved with better parsing\n    const factors = [];\n    if (content.toLowerCase().includes('wrinkle')) factors.push('Facial wrinkles');\n    if (content.toLowerCase().includes('skin')) factors.push('Skin texture');\n    if (content.toLowerCase().includes('hair')) factors.push('Hair condition');\n    if (content.toLowerCase().includes('eye')) factors.push('Eye area');\n    return factors.length > 0 ? factors : [\n        'General facial features'\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n\n\nconst supabaseUrl = \"https://gsuvqpwagpdwwcmtggyy.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdzdXZxcHdhZ3Bkd3djbXRnZ3l5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyMjUzNTcsImV4cCI6MjA2NzgwMTM1N30.32kuYPnA7apzAmEGTwGQVM-FxVSyobDvk-ii8jwWXUY\";\n// Client-side Supabase client\nconst supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey);\n// Server-side Supabase client (for API routes)\nconst createServerClient = ()=>{\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalyze%2Froute&page=%2Fapi%2Fanalyze%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalyze%2Froute.ts&appDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmatti%5CDocuments%5Ccoding%5Cguess-my-age%5Cguess-my-age-new&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();