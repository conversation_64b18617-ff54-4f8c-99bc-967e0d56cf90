{"name": "guess-my-age", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.5", "@supabase/supabase-js": "^2.39.0", "@supabase/ssr": "^0.1.0", "@stripe/stripe-js": "^2.4.0", "stripe": "^14.15.0", "lucide-react": "^0.344.0", "react-dropzone": "^14.2.3"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.5", "@eslint/eslintrc": "^3"}}