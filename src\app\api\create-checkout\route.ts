import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createServerClient } from '@/lib/supabase'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
})

export async function POST(request: NextRequest) {
  try {
    const { credits } = await request.json()

    if (!credits || credits < 1) {
      return NextResponse.json(
        { error: 'Invalid credits amount' },
        { status: 400 }
      )
    }

    // Get user from session
    const supabase = createServerClient()
    const { data: { session } } = await supabase.auth.getSession()
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const userId = session.user.id
    const amountCents = credits * 50 // €0.50 per credit

    // Create Stripe checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price_data: {
            currency: 'eur',
            product_data: {
              name: `${credits} Credits - Guess My Age`,
              description: `Purchase ${credits} credits for photo analysis`,
            },
            unit_amount: amountCents,
          },
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?success=true`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard?canceled=true`,
      metadata: {
        userId,
        credits: credits.toString(),
      },
    })

    // Create pending transaction record
    const { error: dbError } = await supabase
      .from('transactions')
      .insert({
        user_id: userId,
        stripe_payment_intent_id: checkoutSession.payment_intent as string,
        amount_cents: amountCents,
        credits_purchased: credits,
        status: 'pending',
      })

    if (dbError) {
      console.error('Error creating transaction record:', dbError)
      // Don't fail the checkout, but log the error
    }

    return NextResponse.json({ 
      checkoutUrl: checkoutSession.url 
    })

  } catch (error) {
    console.error('Error creating checkout session:', error)
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    )
  }
}
