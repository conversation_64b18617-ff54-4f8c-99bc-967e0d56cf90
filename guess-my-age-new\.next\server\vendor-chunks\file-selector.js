"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/file-selector";
exports.ids = ["vendor-chunks/file-selector"];
exports.modules = {

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js":
/*!*****************************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file-selector.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* binding */ fromEvent)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _file__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file */ \"(ssr)/./node_modules/file-selector/dist/es2015/file.js\");\n\n\nconst FILES_TO_IGNORE = [\n    // Thumbnail cache files for macOS and Windows\n    '.DS_Store', // macOs\n    'Thumbs.db' // Windows\n];\n/**\n * Convert a DragEvent's DataTrasfer object to a list of File objects\n * NOTE: If some of the items are folders,\n * everything will be flattened and placed in the same list but the paths will be kept as a {path} property.\n *\n * EXPERIMENTAL: A list of https://developer.mozilla.org/en-US/docs/Web/API/FileSystemHandle objects can also be passed as an arg\n * and a list of File objects will be returned.\n *\n * @param evt\n */\nfunction fromEvent(evt) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        if (isObject(evt) && isDataTransfer(evt.dataTransfer)) {\n            return getDataTransferFiles(evt.dataTransfer, evt.type);\n        }\n        else if (isChangeEvt(evt)) {\n            return getInputFiles(evt);\n        }\n        else if (Array.isArray(evt) && evt.every(item => 'getFile' in item && typeof item.getFile === 'function')) {\n            return getFsHandleFiles(evt);\n        }\n        return [];\n    });\n}\nfunction isDataTransfer(value) {\n    return isObject(value);\n}\nfunction isChangeEvt(value) {\n    return isObject(value) && isObject(value.target);\n}\nfunction isObject(v) {\n    return typeof v === 'object' && v !== null;\n}\nfunction getInputFiles(evt) {\n    return fromList(evt.target.files).map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n}\n// Ee expect each handle to be https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileHandle\nfunction getFsHandleFiles(handles) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        const files = yield Promise.all(handles.map(h => h.getFile()));\n        return files.map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file));\n    });\n}\nfunction getDataTransferFiles(dt, type) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        // IE11 does not support dataTransfer.items\n        // See https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/items#Browser_compatibility\n        if (dt.items) {\n            const items = fromList(dt.items)\n                .filter(item => item.kind === 'file');\n            // According to https://html.spec.whatwg.org/multipage/dnd.html#dndevents,\n            // only 'dragstart' and 'drop' has access to the data (source node)\n            if (type !== 'drop') {\n                return items;\n            }\n            const files = yield Promise.all(items.map(toFilePromises));\n            return noIgnoredFiles(flatten(files));\n        }\n        return noIgnoredFiles(fromList(dt.files)\n            .map(file => (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file)));\n    });\n}\nfunction noIgnoredFiles(files) {\n    return files.filter(file => FILES_TO_IGNORE.indexOf(file.name) === -1);\n}\n// IE11 does not support Array.from()\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/from#Browser_compatibility\n// https://developer.mozilla.org/en-US/docs/Web/API/FileList\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItemList\nfunction fromList(items) {\n    if (items === null) {\n        return [];\n    }\n    const files = [];\n    // tslint:disable: prefer-for-of\n    for (let i = 0; i < items.length; i++) {\n        const file = items[i];\n        files.push(file);\n    }\n    return files;\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/DataTransferItem\nfunction toFilePromises(item) {\n    if (typeof item.webkitGetAsEntry !== 'function') {\n        return fromDataTransferItem(item);\n    }\n    const entry = item.webkitGetAsEntry();\n    // Safari supports dropping an image node from a different window and can be retrieved using\n    // the DataTransferItem.getAsFile() API\n    // NOTE: FileSystemEntry.file() throws if trying to get the file\n    if (entry && entry.isDirectory) {\n        return fromDirEntry(entry);\n    }\n    return fromDataTransferItem(item, entry);\n}\nfunction flatten(items) {\n    return items.reduce((acc, files) => [\n        ...acc,\n        ...(Array.isArray(files) ? flatten(files) : [files])\n    ], []);\n}\nfunction fromDataTransferItem(item, entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        var _a;\n        // Check if we're in a secure context; due to a bug in Chrome (as far as we know)\n        // the browser crashes when calling this API (yet to be confirmed as a consistent behaviour).\n        //\n        // See:\n        // - https://issues.chromium.org/issues/40186242\n        // - https://github.com/react-dropzone/react-dropzone/issues/1397\n        if (globalThis.isSecureContext && typeof item.getAsFileSystemHandle === 'function') {\n            const h = yield item.getAsFileSystemHandle();\n            if (h === null) {\n                throw new Error(`${item} is not a File`);\n            }\n            // It seems that the handle can be `undefined` (see https://github.com/react-dropzone/file-selector/issues/120),\n            // so we check if it isn't; if it is, the code path continues to the next API (`getAsFile`).\n            if (h !== undefined) {\n                const file = yield h.getFile();\n                file.handle = h;\n                return (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file);\n            }\n        }\n        const file = item.getAsFile();\n        if (!file) {\n            throw new Error(`${item} is not a File`);\n        }\n        const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, (_a = entry === null || entry === void 0 ? void 0 : entry.fullPath) !== null && _a !== void 0 ? _a : undefined);\n        return fwp;\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemEntry\nfunction fromEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        return entry.isDirectory ? fromDirEntry(entry) : fromFileEntry(entry);\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry\nfunction fromDirEntry(entry) {\n    const reader = entry.createReader();\n    return new Promise((resolve, reject) => {\n        const entries = [];\n        function readEntries() {\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryEntry/createReader\n            // https://developer.mozilla.org/en-US/docs/Web/API/FileSystemDirectoryReader/readEntries\n            reader.readEntries((batch) => (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n                if (!batch.length) {\n                    // Done reading directory\n                    try {\n                        const files = yield Promise.all(entries);\n                        resolve(files);\n                    }\n                    catch (err) {\n                        reject(err);\n                    }\n                }\n                else {\n                    const items = Promise.all(batch.map(fromEntry));\n                    entries.push(items);\n                    // Continue reading\n                    readEntries();\n                }\n            }), (err) => {\n                reject(err);\n            });\n        }\n        readEntries();\n    });\n}\n// https://developer.mozilla.org/en-US/docs/Web/API/FileSystemFileEntry\nfunction fromFileEntry(entry) {\n    return (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__awaiter)(this, void 0, void 0, function* () {\n        return new Promise((resolve, reject) => {\n            entry.file((file) => {\n                const fwp = (0,_file__WEBPACK_IMPORTED_MODULE_0__.toFileWithPath)(file, entry.fullPath);\n                resolve(fwp);\n            }, (err) => {\n                reject(err);\n            });\n        });\n    });\n}\n//# sourceMappingURL=file-selector.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/file.js":
/*!********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/file.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMMON_MIME_TYPES: () => (/* binding */ COMMON_MIME_TYPES),\n/* harmony export */   toFileWithPath: () => (/* binding */ toFileWithPath)\n/* harmony export */ });\nconst COMMON_MIME_TYPES = new Map([\n    // https://github.com/guzzle/psr7/blob/2d9260799e713f1c475d3c5fdc3d6561ff7441b2/src/MimeType.php\n    ['1km', 'application/vnd.1000minds.decision-model+xml'],\n    ['3dml', 'text/vnd.in3d.3dml'],\n    ['3ds', 'image/x-3ds'],\n    ['3g2', 'video/3gpp2'],\n    ['3gp', 'video/3gp'],\n    ['3gpp', 'video/3gpp'],\n    ['3mf', 'model/3mf'],\n    ['7z', 'application/x-7z-compressed'],\n    ['7zip', 'application/x-7z-compressed'],\n    ['123', 'application/vnd.lotus-1-2-3'],\n    ['aab', 'application/x-authorware-bin'],\n    ['aac', 'audio/x-acc'],\n    ['aam', 'application/x-authorware-map'],\n    ['aas', 'application/x-authorware-seg'],\n    ['abw', 'application/x-abiword'],\n    ['ac', 'application/vnd.nokia.n-gage.ac+xml'],\n    ['ac3', 'audio/ac3'],\n    ['acc', 'application/vnd.americandynamics.acc'],\n    ['ace', 'application/x-ace-compressed'],\n    ['acu', 'application/vnd.acucobol'],\n    ['acutc', 'application/vnd.acucorp'],\n    ['adp', 'audio/adpcm'],\n    ['aep', 'application/vnd.audiograph'],\n    ['afm', 'application/x-font-type1'],\n    ['afp', 'application/vnd.ibm.modcap'],\n    ['ahead', 'application/vnd.ahead.space'],\n    ['ai', 'application/pdf'],\n    ['aif', 'audio/x-aiff'],\n    ['aifc', 'audio/x-aiff'],\n    ['aiff', 'audio/x-aiff'],\n    ['air', 'application/vnd.adobe.air-application-installer-package+zip'],\n    ['ait', 'application/vnd.dvb.ait'],\n    ['ami', 'application/vnd.amiga.ami'],\n    ['amr', 'audio/amr'],\n    ['apk', 'application/vnd.android.package-archive'],\n    ['apng', 'image/apng'],\n    ['appcache', 'text/cache-manifest'],\n    ['application', 'application/x-ms-application'],\n    ['apr', 'application/vnd.lotus-approach'],\n    ['arc', 'application/x-freearc'],\n    ['arj', 'application/x-arj'],\n    ['asc', 'application/pgp-signature'],\n    ['asf', 'video/x-ms-asf'],\n    ['asm', 'text/x-asm'],\n    ['aso', 'application/vnd.accpac.simply.aso'],\n    ['asx', 'video/x-ms-asf'],\n    ['atc', 'application/vnd.acucorp'],\n    ['atom', 'application/atom+xml'],\n    ['atomcat', 'application/atomcat+xml'],\n    ['atomdeleted', 'application/atomdeleted+xml'],\n    ['atomsvc', 'application/atomsvc+xml'],\n    ['atx', 'application/vnd.antix.game-component'],\n    ['au', 'audio/x-au'],\n    ['avi', 'video/x-msvideo'],\n    ['avif', 'image/avif'],\n    ['aw', 'application/applixware'],\n    ['azf', 'application/vnd.airzip.filesecure.azf'],\n    ['azs', 'application/vnd.airzip.filesecure.azs'],\n    ['azv', 'image/vnd.airzip.accelerator.azv'],\n    ['azw', 'application/vnd.amazon.ebook'],\n    ['b16', 'image/vnd.pco.b16'],\n    ['bat', 'application/x-msdownload'],\n    ['bcpio', 'application/x-bcpio'],\n    ['bdf', 'application/x-font-bdf'],\n    ['bdm', 'application/vnd.syncml.dm+wbxml'],\n    ['bdoc', 'application/x-bdoc'],\n    ['bed', 'application/vnd.realvnc.bed'],\n    ['bh2', 'application/vnd.fujitsu.oasysprs'],\n    ['bin', 'application/octet-stream'],\n    ['blb', 'application/x-blorb'],\n    ['blorb', 'application/x-blorb'],\n    ['bmi', 'application/vnd.bmi'],\n    ['bmml', 'application/vnd.balsamiq.bmml+xml'],\n    ['bmp', 'image/bmp'],\n    ['book', 'application/vnd.framemaker'],\n    ['box', 'application/vnd.previewsystems.box'],\n    ['boz', 'application/x-bzip2'],\n    ['bpk', 'application/octet-stream'],\n    ['bpmn', 'application/octet-stream'],\n    ['bsp', 'model/vnd.valve.source.compiled-map'],\n    ['btif', 'image/prs.btif'],\n    ['buffer', 'application/octet-stream'],\n    ['bz', 'application/x-bzip'],\n    ['bz2', 'application/x-bzip2'],\n    ['c', 'text/x-c'],\n    ['c4d', 'application/vnd.clonk.c4group'],\n    ['c4f', 'application/vnd.clonk.c4group'],\n    ['c4g', 'application/vnd.clonk.c4group'],\n    ['c4p', 'application/vnd.clonk.c4group'],\n    ['c4u', 'application/vnd.clonk.c4group'],\n    ['c11amc', 'application/vnd.cluetrust.cartomobile-config'],\n    ['c11amz', 'application/vnd.cluetrust.cartomobile-config-pkg'],\n    ['cab', 'application/vnd.ms-cab-compressed'],\n    ['caf', 'audio/x-caf'],\n    ['cap', 'application/vnd.tcpdump.pcap'],\n    ['car', 'application/vnd.curl.car'],\n    ['cat', 'application/vnd.ms-pki.seccat'],\n    ['cb7', 'application/x-cbr'],\n    ['cba', 'application/x-cbr'],\n    ['cbr', 'application/x-cbr'],\n    ['cbt', 'application/x-cbr'],\n    ['cbz', 'application/x-cbr'],\n    ['cc', 'text/x-c'],\n    ['cco', 'application/x-cocoa'],\n    ['cct', 'application/x-director'],\n    ['ccxml', 'application/ccxml+xml'],\n    ['cdbcmsg', 'application/vnd.contact.cmsg'],\n    ['cda', 'application/x-cdf'],\n    ['cdf', 'application/x-netcdf'],\n    ['cdfx', 'application/cdfx+xml'],\n    ['cdkey', 'application/vnd.mediastation.cdkey'],\n    ['cdmia', 'application/cdmi-capability'],\n    ['cdmic', 'application/cdmi-container'],\n    ['cdmid', 'application/cdmi-domain'],\n    ['cdmio', 'application/cdmi-object'],\n    ['cdmiq', 'application/cdmi-queue'],\n    ['cdr', 'application/cdr'],\n    ['cdx', 'chemical/x-cdx'],\n    ['cdxml', 'application/vnd.chemdraw+xml'],\n    ['cdy', 'application/vnd.cinderella'],\n    ['cer', 'application/pkix-cert'],\n    ['cfs', 'application/x-cfs-compressed'],\n    ['cgm', 'image/cgm'],\n    ['chat', 'application/x-chat'],\n    ['chm', 'application/vnd.ms-htmlhelp'],\n    ['chrt', 'application/vnd.kde.kchart'],\n    ['cif', 'chemical/x-cif'],\n    ['cii', 'application/vnd.anser-web-certificate-issue-initiation'],\n    ['cil', 'application/vnd.ms-artgalry'],\n    ['cjs', 'application/node'],\n    ['cla', 'application/vnd.claymore'],\n    ['class', 'application/octet-stream'],\n    ['clkk', 'application/vnd.crick.clicker.keyboard'],\n    ['clkp', 'application/vnd.crick.clicker.palette'],\n    ['clkt', 'application/vnd.crick.clicker.template'],\n    ['clkw', 'application/vnd.crick.clicker.wordbank'],\n    ['clkx', 'application/vnd.crick.clicker'],\n    ['clp', 'application/x-msclip'],\n    ['cmc', 'application/vnd.cosmocaller'],\n    ['cmdf', 'chemical/x-cmdf'],\n    ['cml', 'chemical/x-cml'],\n    ['cmp', 'application/vnd.yellowriver-custom-menu'],\n    ['cmx', 'image/x-cmx'],\n    ['cod', 'application/vnd.rim.cod'],\n    ['coffee', 'text/coffeescript'],\n    ['com', 'application/x-msdownload'],\n    ['conf', 'text/plain'],\n    ['cpio', 'application/x-cpio'],\n    ['cpp', 'text/x-c'],\n    ['cpt', 'application/mac-compactpro'],\n    ['crd', 'application/x-mscardfile'],\n    ['crl', 'application/pkix-crl'],\n    ['crt', 'application/x-x509-ca-cert'],\n    ['crx', 'application/x-chrome-extension'],\n    ['cryptonote', 'application/vnd.rig.cryptonote'],\n    ['csh', 'application/x-csh'],\n    ['csl', 'application/vnd.citationstyles.style+xml'],\n    ['csml', 'chemical/x-csml'],\n    ['csp', 'application/vnd.commonspace'],\n    ['csr', 'application/octet-stream'],\n    ['css', 'text/css'],\n    ['cst', 'application/x-director'],\n    ['csv', 'text/csv'],\n    ['cu', 'application/cu-seeme'],\n    ['curl', 'text/vnd.curl'],\n    ['cww', 'application/prs.cww'],\n    ['cxt', 'application/x-director'],\n    ['cxx', 'text/x-c'],\n    ['dae', 'model/vnd.collada+xml'],\n    ['daf', 'application/vnd.mobius.daf'],\n    ['dart', 'application/vnd.dart'],\n    ['dataless', 'application/vnd.fdsn.seed'],\n    ['davmount', 'application/davmount+xml'],\n    ['dbf', 'application/vnd.dbf'],\n    ['dbk', 'application/docbook+xml'],\n    ['dcr', 'application/x-director'],\n    ['dcurl', 'text/vnd.curl.dcurl'],\n    ['dd2', 'application/vnd.oma.dd2+xml'],\n    ['ddd', 'application/vnd.fujixerox.ddd'],\n    ['ddf', 'application/vnd.syncml.dmddf+xml'],\n    ['dds', 'image/vnd.ms-dds'],\n    ['deb', 'application/x-debian-package'],\n    ['def', 'text/plain'],\n    ['deploy', 'application/octet-stream'],\n    ['der', 'application/x-x509-ca-cert'],\n    ['dfac', 'application/vnd.dreamfactory'],\n    ['dgc', 'application/x-dgc-compressed'],\n    ['dic', 'text/x-c'],\n    ['dir', 'application/x-director'],\n    ['dis', 'application/vnd.mobius.dis'],\n    ['disposition-notification', 'message/disposition-notification'],\n    ['dist', 'application/octet-stream'],\n    ['distz', 'application/octet-stream'],\n    ['djv', 'image/vnd.djvu'],\n    ['djvu', 'image/vnd.djvu'],\n    ['dll', 'application/octet-stream'],\n    ['dmg', 'application/x-apple-diskimage'],\n    ['dmn', 'application/octet-stream'],\n    ['dmp', 'application/vnd.tcpdump.pcap'],\n    ['dms', 'application/octet-stream'],\n    ['dna', 'application/vnd.dna'],\n    ['doc', 'application/msword'],\n    ['docm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['docx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],\n    ['dot', 'application/msword'],\n    ['dotm', 'application/vnd.ms-word.template.macroEnabled.12'],\n    ['dotx', 'application/vnd.openxmlformats-officedocument.wordprocessingml.template'],\n    ['dp', 'application/vnd.osgi.dp'],\n    ['dpg', 'application/vnd.dpgraph'],\n    ['dra', 'audio/vnd.dra'],\n    ['drle', 'image/dicom-rle'],\n    ['dsc', 'text/prs.lines.tag'],\n    ['dssc', 'application/dssc+der'],\n    ['dtb', 'application/x-dtbook+xml'],\n    ['dtd', 'application/xml-dtd'],\n    ['dts', 'audio/vnd.dts'],\n    ['dtshd', 'audio/vnd.dts.hd'],\n    ['dump', 'application/octet-stream'],\n    ['dvb', 'video/vnd.dvb.file'],\n    ['dvi', 'application/x-dvi'],\n    ['dwd', 'application/atsc-dwd+xml'],\n    ['dwf', 'model/vnd.dwf'],\n    ['dwg', 'image/vnd.dwg'],\n    ['dxf', 'image/vnd.dxf'],\n    ['dxp', 'application/vnd.spotfire.dxp'],\n    ['dxr', 'application/x-director'],\n    ['ear', 'application/java-archive'],\n    ['ecelp4800', 'audio/vnd.nuera.ecelp4800'],\n    ['ecelp7470', 'audio/vnd.nuera.ecelp7470'],\n    ['ecelp9600', 'audio/vnd.nuera.ecelp9600'],\n    ['ecma', 'application/ecmascript'],\n    ['edm', 'application/vnd.novadigm.edm'],\n    ['edx', 'application/vnd.novadigm.edx'],\n    ['efif', 'application/vnd.picsel'],\n    ['ei6', 'application/vnd.pg.osasli'],\n    ['elc', 'application/octet-stream'],\n    ['emf', 'image/emf'],\n    ['eml', 'message/rfc822'],\n    ['emma', 'application/emma+xml'],\n    ['emotionml', 'application/emotionml+xml'],\n    ['emz', 'application/x-msmetafile'],\n    ['eol', 'audio/vnd.digital-winds'],\n    ['eot', 'application/vnd.ms-fontobject'],\n    ['eps', 'application/postscript'],\n    ['epub', 'application/epub+zip'],\n    ['es', 'application/ecmascript'],\n    ['es3', 'application/vnd.eszigno3+xml'],\n    ['esa', 'application/vnd.osgi.subsystem'],\n    ['esf', 'application/vnd.epson.esf'],\n    ['et3', 'application/vnd.eszigno3+xml'],\n    ['etx', 'text/x-setext'],\n    ['eva', 'application/x-eva'],\n    ['evy', 'application/x-envoy'],\n    ['exe', 'application/octet-stream'],\n    ['exi', 'application/exi'],\n    ['exp', 'application/express'],\n    ['exr', 'image/aces'],\n    ['ext', 'application/vnd.novadigm.ext'],\n    ['ez', 'application/andrew-inset'],\n    ['ez2', 'application/vnd.ezpix-album'],\n    ['ez3', 'application/vnd.ezpix-package'],\n    ['f', 'text/x-fortran'],\n    ['f4v', 'video/mp4'],\n    ['f77', 'text/x-fortran'],\n    ['f90', 'text/x-fortran'],\n    ['fbs', 'image/vnd.fastbidsheet'],\n    ['fcdt', 'application/vnd.adobe.formscentral.fcdt'],\n    ['fcs', 'application/vnd.isac.fcs'],\n    ['fdf', 'application/vnd.fdf'],\n    ['fdt', 'application/fdt+xml'],\n    ['fe_launch', 'application/vnd.denovo.fcselayout-link'],\n    ['fg5', 'application/vnd.fujitsu.oasysgp'],\n    ['fgd', 'application/x-director'],\n    ['fh', 'image/x-freehand'],\n    ['fh4', 'image/x-freehand'],\n    ['fh5', 'image/x-freehand'],\n    ['fh7', 'image/x-freehand'],\n    ['fhc', 'image/x-freehand'],\n    ['fig', 'application/x-xfig'],\n    ['fits', 'image/fits'],\n    ['flac', 'audio/x-flac'],\n    ['fli', 'video/x-fli'],\n    ['flo', 'application/vnd.micrografx.flo'],\n    ['flv', 'video/x-flv'],\n    ['flw', 'application/vnd.kde.kivio'],\n    ['flx', 'text/vnd.fmi.flexstor'],\n    ['fly', 'text/vnd.fly'],\n    ['fm', 'application/vnd.framemaker'],\n    ['fnc', 'application/vnd.frogans.fnc'],\n    ['fo', 'application/vnd.software602.filler.form+xml'],\n    ['for', 'text/x-fortran'],\n    ['fpx', 'image/vnd.fpx'],\n    ['frame', 'application/vnd.framemaker'],\n    ['fsc', 'application/vnd.fsc.weblaunch'],\n    ['fst', 'image/vnd.fst'],\n    ['ftc', 'application/vnd.fluxtime.clip'],\n    ['fti', 'application/vnd.anser-web-funds-transfer-initiation'],\n    ['fvt', 'video/vnd.fvt'],\n    ['fxp', 'application/vnd.adobe.fxp'],\n    ['fxpl', 'application/vnd.adobe.fxp'],\n    ['fzs', 'application/vnd.fuzzysheet'],\n    ['g2w', 'application/vnd.geoplan'],\n    ['g3', 'image/g3fax'],\n    ['g3w', 'application/vnd.geospace'],\n    ['gac', 'application/vnd.groove-account'],\n    ['gam', 'application/x-tads'],\n    ['gbr', 'application/rpki-ghostbusters'],\n    ['gca', 'application/x-gca-compressed'],\n    ['gdl', 'model/vnd.gdl'],\n    ['gdoc', 'application/vnd.google-apps.document'],\n    ['geo', 'application/vnd.dynageo'],\n    ['geojson', 'application/geo+json'],\n    ['gex', 'application/vnd.geometry-explorer'],\n    ['ggb', 'application/vnd.geogebra.file'],\n    ['ggt', 'application/vnd.geogebra.tool'],\n    ['ghf', 'application/vnd.groove-help'],\n    ['gif', 'image/gif'],\n    ['gim', 'application/vnd.groove-identity-message'],\n    ['glb', 'model/gltf-binary'],\n    ['gltf', 'model/gltf+json'],\n    ['gml', 'application/gml+xml'],\n    ['gmx', 'application/vnd.gmx'],\n    ['gnumeric', 'application/x-gnumeric'],\n    ['gpg', 'application/gpg-keys'],\n    ['gph', 'application/vnd.flographit'],\n    ['gpx', 'application/gpx+xml'],\n    ['gqf', 'application/vnd.grafeq'],\n    ['gqs', 'application/vnd.grafeq'],\n    ['gram', 'application/srgs'],\n    ['gramps', 'application/x-gramps-xml'],\n    ['gre', 'application/vnd.geometry-explorer'],\n    ['grv', 'application/vnd.groove-injector'],\n    ['grxml', 'application/srgs+xml'],\n    ['gsf', 'application/x-font-ghostscript'],\n    ['gsheet', 'application/vnd.google-apps.spreadsheet'],\n    ['gslides', 'application/vnd.google-apps.presentation'],\n    ['gtar', 'application/x-gtar'],\n    ['gtm', 'application/vnd.groove-tool-message'],\n    ['gtw', 'model/vnd.gtw'],\n    ['gv', 'text/vnd.graphviz'],\n    ['gxf', 'application/gxf'],\n    ['gxt', 'application/vnd.geonext'],\n    ['gz', 'application/gzip'],\n    ['gzip', 'application/gzip'],\n    ['h', 'text/x-c'],\n    ['h261', 'video/h261'],\n    ['h263', 'video/h263'],\n    ['h264', 'video/h264'],\n    ['hal', 'application/vnd.hal+xml'],\n    ['hbci', 'application/vnd.hbci'],\n    ['hbs', 'text/x-handlebars-template'],\n    ['hdd', 'application/x-virtualbox-hdd'],\n    ['hdf', 'application/x-hdf'],\n    ['heic', 'image/heic'],\n    ['heics', 'image/heic-sequence'],\n    ['heif', 'image/heif'],\n    ['heifs', 'image/heif-sequence'],\n    ['hej2', 'image/hej2k'],\n    ['held', 'application/atsc-held+xml'],\n    ['hh', 'text/x-c'],\n    ['hjson', 'application/hjson'],\n    ['hlp', 'application/winhlp'],\n    ['hpgl', 'application/vnd.hp-hpgl'],\n    ['hpid', 'application/vnd.hp-hpid'],\n    ['hps', 'application/vnd.hp-hps'],\n    ['hqx', 'application/mac-binhex40'],\n    ['hsj2', 'image/hsj2'],\n    ['htc', 'text/x-component'],\n    ['htke', 'application/vnd.kenameaapp'],\n    ['htm', 'text/html'],\n    ['html', 'text/html'],\n    ['hvd', 'application/vnd.yamaha.hv-dic'],\n    ['hvp', 'application/vnd.yamaha.hv-voice'],\n    ['hvs', 'application/vnd.yamaha.hv-script'],\n    ['i2g', 'application/vnd.intergeo'],\n    ['icc', 'application/vnd.iccprofile'],\n    ['ice', 'x-conference/x-cooltalk'],\n    ['icm', 'application/vnd.iccprofile'],\n    ['ico', 'image/x-icon'],\n    ['ics', 'text/calendar'],\n    ['ief', 'image/ief'],\n    ['ifb', 'text/calendar'],\n    ['ifm', 'application/vnd.shana.informed.formdata'],\n    ['iges', 'model/iges'],\n    ['igl', 'application/vnd.igloader'],\n    ['igm', 'application/vnd.insors.igm'],\n    ['igs', 'model/iges'],\n    ['igx', 'application/vnd.micrografx.igx'],\n    ['iif', 'application/vnd.shana.informed.interchange'],\n    ['img', 'application/octet-stream'],\n    ['imp', 'application/vnd.accpac.simply.imp'],\n    ['ims', 'application/vnd.ms-ims'],\n    ['in', 'text/plain'],\n    ['ini', 'text/plain'],\n    ['ink', 'application/inkml+xml'],\n    ['inkml', 'application/inkml+xml'],\n    ['install', 'application/x-install-instructions'],\n    ['iota', 'application/vnd.astraea-software.iota'],\n    ['ipfix', 'application/ipfix'],\n    ['ipk', 'application/vnd.shana.informed.package'],\n    ['irm', 'application/vnd.ibm.rights-management'],\n    ['irp', 'application/vnd.irepository.package+xml'],\n    ['iso', 'application/x-iso9660-image'],\n    ['itp', 'application/vnd.shana.informed.formtemplate'],\n    ['its', 'application/its+xml'],\n    ['ivp', 'application/vnd.immervision-ivp'],\n    ['ivu', 'application/vnd.immervision-ivu'],\n    ['jad', 'text/vnd.sun.j2me.app-descriptor'],\n    ['jade', 'text/jade'],\n    ['jam', 'application/vnd.jam'],\n    ['jar', 'application/java-archive'],\n    ['jardiff', 'application/x-java-archive-diff'],\n    ['java', 'text/x-java-source'],\n    ['jhc', 'image/jphc'],\n    ['jisp', 'application/vnd.jisp'],\n    ['jls', 'image/jls'],\n    ['jlt', 'application/vnd.hp-jlyt'],\n    ['jng', 'image/x-jng'],\n    ['jnlp', 'application/x-java-jnlp-file'],\n    ['joda', 'application/vnd.joost.joda-archive'],\n    ['jp2', 'image/jp2'],\n    ['jpe', 'image/jpeg'],\n    ['jpeg', 'image/jpeg'],\n    ['jpf', 'image/jpx'],\n    ['jpg', 'image/jpeg'],\n    ['jpg2', 'image/jp2'],\n    ['jpgm', 'video/jpm'],\n    ['jpgv', 'video/jpeg'],\n    ['jph', 'image/jph'],\n    ['jpm', 'video/jpm'],\n    ['jpx', 'image/jpx'],\n    ['js', 'application/javascript'],\n    ['json', 'application/json'],\n    ['json5', 'application/json5'],\n    ['jsonld', 'application/ld+json'],\n    // https://jsonlines.org/\n    ['jsonl', 'application/jsonl'],\n    ['jsonml', 'application/jsonml+json'],\n    ['jsx', 'text/jsx'],\n    ['jxr', 'image/jxr'],\n    ['jxra', 'image/jxra'],\n    ['jxrs', 'image/jxrs'],\n    ['jxs', 'image/jxs'],\n    ['jxsc', 'image/jxsc'],\n    ['jxsi', 'image/jxsi'],\n    ['jxss', 'image/jxss'],\n    ['kar', 'audio/midi'],\n    ['karbon', 'application/vnd.kde.karbon'],\n    ['kdb', 'application/octet-stream'],\n    ['kdbx', 'application/x-keepass2'],\n    ['key', 'application/x-iwork-keynote-sffkey'],\n    ['kfo', 'application/vnd.kde.kformula'],\n    ['kia', 'application/vnd.kidspiration'],\n    ['kml', 'application/vnd.google-earth.kml+xml'],\n    ['kmz', 'application/vnd.google-earth.kmz'],\n    ['kne', 'application/vnd.kinar'],\n    ['knp', 'application/vnd.kinar'],\n    ['kon', 'application/vnd.kde.kontour'],\n    ['kpr', 'application/vnd.kde.kpresenter'],\n    ['kpt', 'application/vnd.kde.kpresenter'],\n    ['kpxx', 'application/vnd.ds-keypoint'],\n    ['ksp', 'application/vnd.kde.kspread'],\n    ['ktr', 'application/vnd.kahootz'],\n    ['ktx', 'image/ktx'],\n    ['ktx2', 'image/ktx2'],\n    ['ktz', 'application/vnd.kahootz'],\n    ['kwd', 'application/vnd.kde.kword'],\n    ['kwt', 'application/vnd.kde.kword'],\n    ['lasxml', 'application/vnd.las.las+xml'],\n    ['latex', 'application/x-latex'],\n    ['lbd', 'application/vnd.llamagraphics.life-balance.desktop'],\n    ['lbe', 'application/vnd.llamagraphics.life-balance.exchange+xml'],\n    ['les', 'application/vnd.hhe.lesson-player'],\n    ['less', 'text/less'],\n    ['lgr', 'application/lgr+xml'],\n    ['lha', 'application/octet-stream'],\n    ['link66', 'application/vnd.route66.link66+xml'],\n    ['list', 'text/plain'],\n    ['list3820', 'application/vnd.ibm.modcap'],\n    ['listafp', 'application/vnd.ibm.modcap'],\n    ['litcoffee', 'text/coffeescript'],\n    ['lnk', 'application/x-ms-shortcut'],\n    ['log', 'text/plain'],\n    ['lostxml', 'application/lost+xml'],\n    ['lrf', 'application/octet-stream'],\n    ['lrm', 'application/vnd.ms-lrm'],\n    ['ltf', 'application/vnd.frogans.ltf'],\n    ['lua', 'text/x-lua'],\n    ['luac', 'application/x-lua-bytecode'],\n    ['lvp', 'audio/vnd.lucent.voice'],\n    ['lwp', 'application/vnd.lotus-wordpro'],\n    ['lzh', 'application/octet-stream'],\n    ['m1v', 'video/mpeg'],\n    ['m2a', 'audio/mpeg'],\n    ['m2v', 'video/mpeg'],\n    ['m3a', 'audio/mpeg'],\n    ['m3u', 'text/plain'],\n    ['m3u8', 'application/vnd.apple.mpegurl'],\n    ['m4a', 'audio/x-m4a'],\n    ['m4p', 'application/mp4'],\n    ['m4s', 'video/iso.segment'],\n    ['m4u', 'application/vnd.mpegurl'],\n    ['m4v', 'video/x-m4v'],\n    ['m13', 'application/x-msmediaview'],\n    ['m14', 'application/x-msmediaview'],\n    ['m21', 'application/mp21'],\n    ['ma', 'application/mathematica'],\n    ['mads', 'application/mads+xml'],\n    ['maei', 'application/mmt-aei+xml'],\n    ['mag', 'application/vnd.ecowin.chart'],\n    ['maker', 'application/vnd.framemaker'],\n    ['man', 'text/troff'],\n    ['manifest', 'text/cache-manifest'],\n    ['map', 'application/json'],\n    ['mar', 'application/octet-stream'],\n    ['markdown', 'text/markdown'],\n    ['mathml', 'application/mathml+xml'],\n    ['mb', 'application/mathematica'],\n    ['mbk', 'application/vnd.mobius.mbk'],\n    ['mbox', 'application/mbox'],\n    ['mc1', 'application/vnd.medcalcdata'],\n    ['mcd', 'application/vnd.mcd'],\n    ['mcurl', 'text/vnd.curl.mcurl'],\n    ['md', 'text/markdown'],\n    ['mdb', 'application/x-msaccess'],\n    ['mdi', 'image/vnd.ms-modi'],\n    ['mdx', 'text/mdx'],\n    ['me', 'text/troff'],\n    ['mesh', 'model/mesh'],\n    ['meta4', 'application/metalink4+xml'],\n    ['metalink', 'application/metalink+xml'],\n    ['mets', 'application/mets+xml'],\n    ['mfm', 'application/vnd.mfmp'],\n    ['mft', 'application/rpki-manifest'],\n    ['mgp', 'application/vnd.osgeo.mapguide.package'],\n    ['mgz', 'application/vnd.proteus.magazine'],\n    ['mid', 'audio/midi'],\n    ['midi', 'audio/midi'],\n    ['mie', 'application/x-mie'],\n    ['mif', 'application/vnd.mif'],\n    ['mime', 'message/rfc822'],\n    ['mj2', 'video/mj2'],\n    ['mjp2', 'video/mj2'],\n    ['mjs', 'application/javascript'],\n    ['mk3d', 'video/x-matroska'],\n    ['mka', 'audio/x-matroska'],\n    ['mkd', 'text/x-markdown'],\n    ['mks', 'video/x-matroska'],\n    ['mkv', 'video/x-matroska'],\n    ['mlp', 'application/vnd.dolby.mlp'],\n    ['mmd', 'application/vnd.chipnuts.karaoke-mmd'],\n    ['mmf', 'application/vnd.smaf'],\n    ['mml', 'text/mathml'],\n    ['mmr', 'image/vnd.fujixerox.edmics-mmr'],\n    ['mng', 'video/x-mng'],\n    ['mny', 'application/x-msmoney'],\n    ['mobi', 'application/x-mobipocket-ebook'],\n    ['mods', 'application/mods+xml'],\n    ['mov', 'video/quicktime'],\n    ['movie', 'video/x-sgi-movie'],\n    ['mp2', 'audio/mpeg'],\n    ['mp2a', 'audio/mpeg'],\n    ['mp3', 'audio/mpeg'],\n    ['mp4', 'video/mp4'],\n    ['mp4a', 'audio/mp4'],\n    ['mp4s', 'application/mp4'],\n    ['mp4v', 'video/mp4'],\n    ['mp21', 'application/mp21'],\n    ['mpc', 'application/vnd.mophun.certificate'],\n    ['mpd', 'application/dash+xml'],\n    ['mpe', 'video/mpeg'],\n    ['mpeg', 'video/mpeg'],\n    ['mpg', 'video/mpeg'],\n    ['mpg4', 'video/mp4'],\n    ['mpga', 'audio/mpeg'],\n    ['mpkg', 'application/vnd.apple.installer+xml'],\n    ['mpm', 'application/vnd.blueice.multipass'],\n    ['mpn', 'application/vnd.mophun.application'],\n    ['mpp', 'application/vnd.ms-project'],\n    ['mpt', 'application/vnd.ms-project'],\n    ['mpy', 'application/vnd.ibm.minipay'],\n    ['mqy', 'application/vnd.mobius.mqy'],\n    ['mrc', 'application/marc'],\n    ['mrcx', 'application/marcxml+xml'],\n    ['ms', 'text/troff'],\n    ['mscml', 'application/mediaservercontrol+xml'],\n    ['mseed', 'application/vnd.fdsn.mseed'],\n    ['mseq', 'application/vnd.mseq'],\n    ['msf', 'application/vnd.epson.msf'],\n    ['msg', 'application/vnd.ms-outlook'],\n    ['msh', 'model/mesh'],\n    ['msi', 'application/x-msdownload'],\n    ['msl', 'application/vnd.mobius.msl'],\n    ['msm', 'application/octet-stream'],\n    ['msp', 'application/octet-stream'],\n    ['msty', 'application/vnd.muvee.style'],\n    ['mtl', 'model/mtl'],\n    ['mts', 'model/vnd.mts'],\n    ['mus', 'application/vnd.musician'],\n    ['musd', 'application/mmt-usd+xml'],\n    ['musicxml', 'application/vnd.recordare.musicxml+xml'],\n    ['mvb', 'application/x-msmediaview'],\n    ['mvt', 'application/vnd.mapbox-vector-tile'],\n    ['mwf', 'application/vnd.mfer'],\n    ['mxf', 'application/mxf'],\n    ['mxl', 'application/vnd.recordare.musicxml'],\n    ['mxmf', 'audio/mobile-xmf'],\n    ['mxml', 'application/xv+xml'],\n    ['mxs', 'application/vnd.triscape.mxs'],\n    ['mxu', 'video/vnd.mpegurl'],\n    ['n-gage', 'application/vnd.nokia.n-gage.symbian.install'],\n    ['n3', 'text/n3'],\n    ['nb', 'application/mathematica'],\n    ['nbp', 'application/vnd.wolfram.player'],\n    ['nc', 'application/x-netcdf'],\n    ['ncx', 'application/x-dtbncx+xml'],\n    ['nfo', 'text/x-nfo'],\n    ['ngdat', 'application/vnd.nokia.n-gage.data'],\n    ['nitf', 'application/vnd.nitf'],\n    ['nlu', 'application/vnd.neurolanguage.nlu'],\n    ['nml', 'application/vnd.enliven'],\n    ['nnd', 'application/vnd.noblenet-directory'],\n    ['nns', 'application/vnd.noblenet-sealer'],\n    ['nnw', 'application/vnd.noblenet-web'],\n    ['npx', 'image/vnd.net-fpx'],\n    ['nq', 'application/n-quads'],\n    ['nsc', 'application/x-conference'],\n    ['nsf', 'application/vnd.lotus-notes'],\n    ['nt', 'application/n-triples'],\n    ['ntf', 'application/vnd.nitf'],\n    ['numbers', 'application/x-iwork-numbers-sffnumbers'],\n    ['nzb', 'application/x-nzb'],\n    ['oa2', 'application/vnd.fujitsu.oasys2'],\n    ['oa3', 'application/vnd.fujitsu.oasys3'],\n    ['oas', 'application/vnd.fujitsu.oasys'],\n    ['obd', 'application/x-msbinder'],\n    ['obgx', 'application/vnd.openblox.game+xml'],\n    ['obj', 'model/obj'],\n    ['oda', 'application/oda'],\n    ['odb', 'application/vnd.oasis.opendocument.database'],\n    ['odc', 'application/vnd.oasis.opendocument.chart'],\n    ['odf', 'application/vnd.oasis.opendocument.formula'],\n    ['odft', 'application/vnd.oasis.opendocument.formula-template'],\n    ['odg', 'application/vnd.oasis.opendocument.graphics'],\n    ['odi', 'application/vnd.oasis.opendocument.image'],\n    ['odm', 'application/vnd.oasis.opendocument.text-master'],\n    ['odp', 'application/vnd.oasis.opendocument.presentation'],\n    ['ods', 'application/vnd.oasis.opendocument.spreadsheet'],\n    ['odt', 'application/vnd.oasis.opendocument.text'],\n    ['oga', 'audio/ogg'],\n    ['ogex', 'model/vnd.opengex'],\n    ['ogg', 'audio/ogg'],\n    ['ogv', 'video/ogg'],\n    ['ogx', 'application/ogg'],\n    ['omdoc', 'application/omdoc+xml'],\n    ['onepkg', 'application/onenote'],\n    ['onetmp', 'application/onenote'],\n    ['onetoc', 'application/onenote'],\n    ['onetoc2', 'application/onenote'],\n    ['opf', 'application/oebps-package+xml'],\n    ['opml', 'text/x-opml'],\n    ['oprc', 'application/vnd.palm'],\n    ['opus', 'audio/ogg'],\n    ['org', 'text/x-org'],\n    ['osf', 'application/vnd.yamaha.openscoreformat'],\n    ['osfpvg', 'application/vnd.yamaha.openscoreformat.osfpvg+xml'],\n    ['osm', 'application/vnd.openstreetmap.data+xml'],\n    ['otc', 'application/vnd.oasis.opendocument.chart-template'],\n    ['otf', 'font/otf'],\n    ['otg', 'application/vnd.oasis.opendocument.graphics-template'],\n    ['oth', 'application/vnd.oasis.opendocument.text-web'],\n    ['oti', 'application/vnd.oasis.opendocument.image-template'],\n    ['otp', 'application/vnd.oasis.opendocument.presentation-template'],\n    ['ots', 'application/vnd.oasis.opendocument.spreadsheet-template'],\n    ['ott', 'application/vnd.oasis.opendocument.text-template'],\n    ['ova', 'application/x-virtualbox-ova'],\n    ['ovf', 'application/x-virtualbox-ovf'],\n    ['owl', 'application/rdf+xml'],\n    ['oxps', 'application/oxps'],\n    ['oxt', 'application/vnd.openofficeorg.extension'],\n    ['p', 'text/x-pascal'],\n    ['p7a', 'application/x-pkcs7-signature'],\n    ['p7b', 'application/x-pkcs7-certificates'],\n    ['p7c', 'application/pkcs7-mime'],\n    ['p7m', 'application/pkcs7-mime'],\n    ['p7r', 'application/x-pkcs7-certreqresp'],\n    ['p7s', 'application/pkcs7-signature'],\n    ['p8', 'application/pkcs8'],\n    ['p10', 'application/x-pkcs10'],\n    ['p12', 'application/x-pkcs12'],\n    ['pac', 'application/x-ns-proxy-autoconfig'],\n    ['pages', 'application/x-iwork-pages-sffpages'],\n    ['pas', 'text/x-pascal'],\n    ['paw', 'application/vnd.pawaafile'],\n    ['pbd', 'application/vnd.powerbuilder6'],\n    ['pbm', 'image/x-portable-bitmap'],\n    ['pcap', 'application/vnd.tcpdump.pcap'],\n    ['pcf', 'application/x-font-pcf'],\n    ['pcl', 'application/vnd.hp-pcl'],\n    ['pclxl', 'application/vnd.hp-pclxl'],\n    ['pct', 'image/x-pict'],\n    ['pcurl', 'application/vnd.curl.pcurl'],\n    ['pcx', 'image/x-pcx'],\n    ['pdb', 'application/x-pilot'],\n    ['pde', 'text/x-processing'],\n    ['pdf', 'application/pdf'],\n    ['pem', 'application/x-x509-user-cert'],\n    ['pfa', 'application/x-font-type1'],\n    ['pfb', 'application/x-font-type1'],\n    ['pfm', 'application/x-font-type1'],\n    ['pfr', 'application/font-tdpfr'],\n    ['pfx', 'application/x-pkcs12'],\n    ['pgm', 'image/x-portable-graymap'],\n    ['pgn', 'application/x-chess-pgn'],\n    ['pgp', 'application/pgp'],\n    ['php', 'application/x-httpd-php'],\n    ['php3', 'application/x-httpd-php'],\n    ['php4', 'application/x-httpd-php'],\n    ['phps', 'application/x-httpd-php-source'],\n    ['phtml', 'application/x-httpd-php'],\n    ['pic', 'image/x-pict'],\n    ['pkg', 'application/octet-stream'],\n    ['pki', 'application/pkixcmp'],\n    ['pkipath', 'application/pkix-pkipath'],\n    ['pkpass', 'application/vnd.apple.pkpass'],\n    ['pl', 'application/x-perl'],\n    ['plb', 'application/vnd.3gpp.pic-bw-large'],\n    ['plc', 'application/vnd.mobius.plc'],\n    ['plf', 'application/vnd.pocketlearn'],\n    ['pls', 'application/pls+xml'],\n    ['pm', 'application/x-perl'],\n    ['pml', 'application/vnd.ctc-posml'],\n    ['png', 'image/png'],\n    ['pnm', 'image/x-portable-anymap'],\n    ['portpkg', 'application/vnd.macports.portpkg'],\n    ['pot', 'application/vnd.ms-powerpoint'],\n    ['potm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['potx', 'application/vnd.openxmlformats-officedocument.presentationml.template'],\n    ['ppa', 'application/vnd.ms-powerpoint'],\n    ['ppam', 'application/vnd.ms-powerpoint.addin.macroEnabled.12'],\n    ['ppd', 'application/vnd.cups-ppd'],\n    ['ppm', 'image/x-portable-pixmap'],\n    ['pps', 'application/vnd.ms-powerpoint'],\n    ['ppsm', 'application/vnd.ms-powerpoint.slideshow.macroEnabled.12'],\n    ['ppsx', 'application/vnd.openxmlformats-officedocument.presentationml.slideshow'],\n    ['ppt', 'application/powerpoint'],\n    ['pptm', 'application/vnd.ms-powerpoint.presentation.macroEnabled.12'],\n    ['pptx', 'application/vnd.openxmlformats-officedocument.presentationml.presentation'],\n    ['pqa', 'application/vnd.palm'],\n    ['prc', 'application/x-pilot'],\n    ['pre', 'application/vnd.lotus-freelance'],\n    ['prf', 'application/pics-rules'],\n    ['provx', 'application/provenance+xml'],\n    ['ps', 'application/postscript'],\n    ['psb', 'application/vnd.3gpp.pic-bw-small'],\n    ['psd', 'application/x-photoshop'],\n    ['psf', 'application/x-font-linux-psf'],\n    ['pskcxml', 'application/pskc+xml'],\n    ['pti', 'image/prs.pti'],\n    ['ptid', 'application/vnd.pvi.ptid1'],\n    ['pub', 'application/x-mspublisher'],\n    ['pvb', 'application/vnd.3gpp.pic-bw-var'],\n    ['pwn', 'application/vnd.3m.post-it-notes'],\n    ['pya', 'audio/vnd.ms-playready.media.pya'],\n    ['pyv', 'video/vnd.ms-playready.media.pyv'],\n    ['qam', 'application/vnd.epson.quickanime'],\n    ['qbo', 'application/vnd.intu.qbo'],\n    ['qfx', 'application/vnd.intu.qfx'],\n    ['qps', 'application/vnd.publishare-delta-tree'],\n    ['qt', 'video/quicktime'],\n    ['qwd', 'application/vnd.quark.quarkxpress'],\n    ['qwt', 'application/vnd.quark.quarkxpress'],\n    ['qxb', 'application/vnd.quark.quarkxpress'],\n    ['qxd', 'application/vnd.quark.quarkxpress'],\n    ['qxl', 'application/vnd.quark.quarkxpress'],\n    ['qxt', 'application/vnd.quark.quarkxpress'],\n    ['ra', 'audio/x-realaudio'],\n    ['ram', 'audio/x-pn-realaudio'],\n    ['raml', 'application/raml+yaml'],\n    ['rapd', 'application/route-apd+xml'],\n    ['rar', 'application/x-rar'],\n    ['ras', 'image/x-cmu-raster'],\n    ['rcprofile', 'application/vnd.ipunplugged.rcprofile'],\n    ['rdf', 'application/rdf+xml'],\n    ['rdz', 'application/vnd.data-vision.rdz'],\n    ['relo', 'application/p2p-overlay+xml'],\n    ['rep', 'application/vnd.businessobjects'],\n    ['res', 'application/x-dtbresource+xml'],\n    ['rgb', 'image/x-rgb'],\n    ['rif', 'application/reginfo+xml'],\n    ['rip', 'audio/vnd.rip'],\n    ['ris', 'application/x-research-info-systems'],\n    ['rl', 'application/resource-lists+xml'],\n    ['rlc', 'image/vnd.fujixerox.edmics-rlc'],\n    ['rld', 'application/resource-lists-diff+xml'],\n    ['rm', 'audio/x-pn-realaudio'],\n    ['rmi', 'audio/midi'],\n    ['rmp', 'audio/x-pn-realaudio-plugin'],\n    ['rms', 'application/vnd.jcp.javame.midlet-rms'],\n    ['rmvb', 'application/vnd.rn-realmedia-vbr'],\n    ['rnc', 'application/relax-ng-compact-syntax'],\n    ['rng', 'application/xml'],\n    ['roa', 'application/rpki-roa'],\n    ['roff', 'text/troff'],\n    ['rp9', 'application/vnd.cloanto.rp9'],\n    ['rpm', 'audio/x-pn-realaudio-plugin'],\n    ['rpss', 'application/vnd.nokia.radio-presets'],\n    ['rpst', 'application/vnd.nokia.radio-preset'],\n    ['rq', 'application/sparql-query'],\n    ['rs', 'application/rls-services+xml'],\n    ['rsa', 'application/x-pkcs7'],\n    ['rsat', 'application/atsc-rsat+xml'],\n    ['rsd', 'application/rsd+xml'],\n    ['rsheet', 'application/urc-ressheet+xml'],\n    ['rss', 'application/rss+xml'],\n    ['rtf', 'text/rtf'],\n    ['rtx', 'text/richtext'],\n    ['run', 'application/x-makeself'],\n    ['rusd', 'application/route-usd+xml'],\n    ['rv', 'video/vnd.rn-realvideo'],\n    ['s', 'text/x-asm'],\n    ['s3m', 'audio/s3m'],\n    ['saf', 'application/vnd.yamaha.smaf-audio'],\n    ['sass', 'text/x-sass'],\n    ['sbml', 'application/sbml+xml'],\n    ['sc', 'application/vnd.ibm.secure-container'],\n    ['scd', 'application/x-msschedule'],\n    ['scm', 'application/vnd.lotus-screencam'],\n    ['scq', 'application/scvp-cv-request'],\n    ['scs', 'application/scvp-cv-response'],\n    ['scss', 'text/x-scss'],\n    ['scurl', 'text/vnd.curl.scurl'],\n    ['sda', 'application/vnd.stardivision.draw'],\n    ['sdc', 'application/vnd.stardivision.calc'],\n    ['sdd', 'application/vnd.stardivision.impress'],\n    ['sdkd', 'application/vnd.solent.sdkm+xml'],\n    ['sdkm', 'application/vnd.solent.sdkm+xml'],\n    ['sdp', 'application/sdp'],\n    ['sdw', 'application/vnd.stardivision.writer'],\n    ['sea', 'application/octet-stream'],\n    ['see', 'application/vnd.seemail'],\n    ['seed', 'application/vnd.fdsn.seed'],\n    ['sema', 'application/vnd.sema'],\n    ['semd', 'application/vnd.semd'],\n    ['semf', 'application/vnd.semf'],\n    ['senmlx', 'application/senml+xml'],\n    ['sensmlx', 'application/sensml+xml'],\n    ['ser', 'application/java-serialized-object'],\n    ['setpay', 'application/set-payment-initiation'],\n    ['setreg', 'application/set-registration-initiation'],\n    ['sfd-hdstx', 'application/vnd.hydrostatix.sof-data'],\n    ['sfs', 'application/vnd.spotfire.sfs'],\n    ['sfv', 'text/x-sfv'],\n    ['sgi', 'image/sgi'],\n    ['sgl', 'application/vnd.stardivision.writer-global'],\n    ['sgm', 'text/sgml'],\n    ['sgml', 'text/sgml'],\n    ['sh', 'application/x-sh'],\n    ['shar', 'application/x-shar'],\n    ['shex', 'text/shex'],\n    ['shf', 'application/shf+xml'],\n    ['shtml', 'text/html'],\n    ['sid', 'image/x-mrsid-image'],\n    ['sieve', 'application/sieve'],\n    ['sig', 'application/pgp-signature'],\n    ['sil', 'audio/silk'],\n    ['silo', 'model/mesh'],\n    ['sis', 'application/vnd.symbian.install'],\n    ['sisx', 'application/vnd.symbian.install'],\n    ['sit', 'application/x-stuffit'],\n    ['sitx', 'application/x-stuffitx'],\n    ['siv', 'application/sieve'],\n    ['skd', 'application/vnd.koan'],\n    ['skm', 'application/vnd.koan'],\n    ['skp', 'application/vnd.koan'],\n    ['skt', 'application/vnd.koan'],\n    ['sldm', 'application/vnd.ms-powerpoint.slide.macroenabled.12'],\n    ['sldx', 'application/vnd.openxmlformats-officedocument.presentationml.slide'],\n    ['slim', 'text/slim'],\n    ['slm', 'text/slim'],\n    ['sls', 'application/route-s-tsid+xml'],\n    ['slt', 'application/vnd.epson.salt'],\n    ['sm', 'application/vnd.stepmania.stepchart'],\n    ['smf', 'application/vnd.stardivision.math'],\n    ['smi', 'application/smil'],\n    ['smil', 'application/smil'],\n    ['smv', 'video/x-smv'],\n    ['smzip', 'application/vnd.stepmania.package'],\n    ['snd', 'audio/basic'],\n    ['snf', 'application/x-font-snf'],\n    ['so', 'application/octet-stream'],\n    ['spc', 'application/x-pkcs7-certificates'],\n    ['spdx', 'text/spdx'],\n    ['spf', 'application/vnd.yamaha.smaf-phrase'],\n    ['spl', 'application/x-futuresplash'],\n    ['spot', 'text/vnd.in3d.spot'],\n    ['spp', 'application/scvp-vp-response'],\n    ['spq', 'application/scvp-vp-request'],\n    ['spx', 'audio/ogg'],\n    ['sql', 'application/x-sql'],\n    ['src', 'application/x-wais-source'],\n    ['srt', 'application/x-subrip'],\n    ['sru', 'application/sru+xml'],\n    ['srx', 'application/sparql-results+xml'],\n    ['ssdl', 'application/ssdl+xml'],\n    ['sse', 'application/vnd.kodak-descriptor'],\n    ['ssf', 'application/vnd.epson.ssf'],\n    ['ssml', 'application/ssml+xml'],\n    ['sst', 'application/octet-stream'],\n    ['st', 'application/vnd.sailingtracker.track'],\n    ['stc', 'application/vnd.sun.xml.calc.template'],\n    ['std', 'application/vnd.sun.xml.draw.template'],\n    ['stf', 'application/vnd.wt.stf'],\n    ['sti', 'application/vnd.sun.xml.impress.template'],\n    ['stk', 'application/hyperstudio'],\n    ['stl', 'model/stl'],\n    ['stpx', 'model/step+xml'],\n    ['stpxz', 'model/step-xml+zip'],\n    ['stpz', 'model/step+zip'],\n    ['str', 'application/vnd.pg.format'],\n    ['stw', 'application/vnd.sun.xml.writer.template'],\n    ['styl', 'text/stylus'],\n    ['stylus', 'text/stylus'],\n    ['sub', 'text/vnd.dvb.subtitle'],\n    ['sus', 'application/vnd.sus-calendar'],\n    ['susp', 'application/vnd.sus-calendar'],\n    ['sv4cpio', 'application/x-sv4cpio'],\n    ['sv4crc', 'application/x-sv4crc'],\n    ['svc', 'application/vnd.dvb.service'],\n    ['svd', 'application/vnd.svd'],\n    ['svg', 'image/svg+xml'],\n    ['svgz', 'image/svg+xml'],\n    ['swa', 'application/x-director'],\n    ['swf', 'application/x-shockwave-flash'],\n    ['swi', 'application/vnd.aristanetworks.swi'],\n    ['swidtag', 'application/swid+xml'],\n    ['sxc', 'application/vnd.sun.xml.calc'],\n    ['sxd', 'application/vnd.sun.xml.draw'],\n    ['sxg', 'application/vnd.sun.xml.writer.global'],\n    ['sxi', 'application/vnd.sun.xml.impress'],\n    ['sxm', 'application/vnd.sun.xml.math'],\n    ['sxw', 'application/vnd.sun.xml.writer'],\n    ['t', 'text/troff'],\n    ['t3', 'application/x-t3vm-image'],\n    ['t38', 'image/t38'],\n    ['taglet', 'application/vnd.mynfc'],\n    ['tao', 'application/vnd.tao.intent-module-archive'],\n    ['tap', 'image/vnd.tencent.tap'],\n    ['tar', 'application/x-tar'],\n    ['tcap', 'application/vnd.3gpp2.tcap'],\n    ['tcl', 'application/x-tcl'],\n    ['td', 'application/urc-targetdesc+xml'],\n    ['teacher', 'application/vnd.smart.teacher'],\n    ['tei', 'application/tei+xml'],\n    ['teicorpus', 'application/tei+xml'],\n    ['tex', 'application/x-tex'],\n    ['texi', 'application/x-texinfo'],\n    ['texinfo', 'application/x-texinfo'],\n    ['text', 'text/plain'],\n    ['tfi', 'application/thraud+xml'],\n    ['tfm', 'application/x-tex-tfm'],\n    ['tfx', 'image/tiff-fx'],\n    ['tga', 'image/x-tga'],\n    ['tgz', 'application/x-tar'],\n    ['thmx', 'application/vnd.ms-officetheme'],\n    ['tif', 'image/tiff'],\n    ['tiff', 'image/tiff'],\n    ['tk', 'application/x-tcl'],\n    ['tmo', 'application/vnd.tmobile-livetv'],\n    ['toml', 'application/toml'],\n    ['torrent', 'application/x-bittorrent'],\n    ['tpl', 'application/vnd.groove-tool-template'],\n    ['tpt', 'application/vnd.trid.tpt'],\n    ['tr', 'text/troff'],\n    ['tra', 'application/vnd.trueapp'],\n    ['trig', 'application/trig'],\n    ['trm', 'application/x-msterminal'],\n    ['ts', 'video/mp2t'],\n    ['tsd', 'application/timestamped-data'],\n    ['tsv', 'text/tab-separated-values'],\n    ['ttc', 'font/collection'],\n    ['ttf', 'font/ttf'],\n    ['ttl', 'text/turtle'],\n    ['ttml', 'application/ttml+xml'],\n    ['twd', 'application/vnd.simtech-mindmapper'],\n    ['twds', 'application/vnd.simtech-mindmapper'],\n    ['txd', 'application/vnd.genomatix.tuxedo'],\n    ['txf', 'application/vnd.mobius.txf'],\n    ['txt', 'text/plain'],\n    ['u8dsn', 'message/global-delivery-status'],\n    ['u8hdr', 'message/global-headers'],\n    ['u8mdn', 'message/global-disposition-notification'],\n    ['u8msg', 'message/global'],\n    ['u32', 'application/x-authorware-bin'],\n    ['ubj', 'application/ubjson'],\n    ['udeb', 'application/x-debian-package'],\n    ['ufd', 'application/vnd.ufdl'],\n    ['ufdl', 'application/vnd.ufdl'],\n    ['ulx', 'application/x-glulx'],\n    ['umj', 'application/vnd.umajin'],\n    ['unityweb', 'application/vnd.unity'],\n    ['uoml', 'application/vnd.uoml+xml'],\n    ['uri', 'text/uri-list'],\n    ['uris', 'text/uri-list'],\n    ['urls', 'text/uri-list'],\n    ['usdz', 'model/vnd.usdz+zip'],\n    ['ustar', 'application/x-ustar'],\n    ['utz', 'application/vnd.uiq.theme'],\n    ['uu', 'text/x-uuencode'],\n    ['uva', 'audio/vnd.dece.audio'],\n    ['uvd', 'application/vnd.dece.data'],\n    ['uvf', 'application/vnd.dece.data'],\n    ['uvg', 'image/vnd.dece.graphic'],\n    ['uvh', 'video/vnd.dece.hd'],\n    ['uvi', 'image/vnd.dece.graphic'],\n    ['uvm', 'video/vnd.dece.mobile'],\n    ['uvp', 'video/vnd.dece.pd'],\n    ['uvs', 'video/vnd.dece.sd'],\n    ['uvt', 'application/vnd.dece.ttml+xml'],\n    ['uvu', 'video/vnd.uvvu.mp4'],\n    ['uvv', 'video/vnd.dece.video'],\n    ['uvva', 'audio/vnd.dece.audio'],\n    ['uvvd', 'application/vnd.dece.data'],\n    ['uvvf', 'application/vnd.dece.data'],\n    ['uvvg', 'image/vnd.dece.graphic'],\n    ['uvvh', 'video/vnd.dece.hd'],\n    ['uvvi', 'image/vnd.dece.graphic'],\n    ['uvvm', 'video/vnd.dece.mobile'],\n    ['uvvp', 'video/vnd.dece.pd'],\n    ['uvvs', 'video/vnd.dece.sd'],\n    ['uvvt', 'application/vnd.dece.ttml+xml'],\n    ['uvvu', 'video/vnd.uvvu.mp4'],\n    ['uvvv', 'video/vnd.dece.video'],\n    ['uvvx', 'application/vnd.dece.unspecified'],\n    ['uvvz', 'application/vnd.dece.zip'],\n    ['uvx', 'application/vnd.dece.unspecified'],\n    ['uvz', 'application/vnd.dece.zip'],\n    ['vbox', 'application/x-virtualbox-vbox'],\n    ['vbox-extpack', 'application/x-virtualbox-vbox-extpack'],\n    ['vcard', 'text/vcard'],\n    ['vcd', 'application/x-cdlink'],\n    ['vcf', 'text/x-vcard'],\n    ['vcg', 'application/vnd.groove-vcard'],\n    ['vcs', 'text/x-vcalendar'],\n    ['vcx', 'application/vnd.vcx'],\n    ['vdi', 'application/x-virtualbox-vdi'],\n    ['vds', 'model/vnd.sap.vds'],\n    ['vhd', 'application/x-virtualbox-vhd'],\n    ['vis', 'application/vnd.visionary'],\n    ['viv', 'video/vnd.vivo'],\n    ['vlc', 'application/videolan'],\n    ['vmdk', 'application/x-virtualbox-vmdk'],\n    ['vob', 'video/x-ms-vob'],\n    ['vor', 'application/vnd.stardivision.writer'],\n    ['vox', 'application/x-authorware-bin'],\n    ['vrml', 'model/vrml'],\n    ['vsd', 'application/vnd.visio'],\n    ['vsf', 'application/vnd.vsf'],\n    ['vss', 'application/vnd.visio'],\n    ['vst', 'application/vnd.visio'],\n    ['vsw', 'application/vnd.visio'],\n    ['vtf', 'image/vnd.valve.source.texture'],\n    ['vtt', 'text/vtt'],\n    ['vtu', 'model/vnd.vtu'],\n    ['vxml', 'application/voicexml+xml'],\n    ['w3d', 'application/x-director'],\n    ['wad', 'application/x-doom'],\n    ['wadl', 'application/vnd.sun.wadl+xml'],\n    ['war', 'application/java-archive'],\n    ['wasm', 'application/wasm'],\n    ['wav', 'audio/x-wav'],\n    ['wax', 'audio/x-ms-wax'],\n    ['wbmp', 'image/vnd.wap.wbmp'],\n    ['wbs', 'application/vnd.criticaltools.wbs+xml'],\n    ['wbxml', 'application/wbxml'],\n    ['wcm', 'application/vnd.ms-works'],\n    ['wdb', 'application/vnd.ms-works'],\n    ['wdp', 'image/vnd.ms-photo'],\n    ['weba', 'audio/webm'],\n    ['webapp', 'application/x-web-app-manifest+json'],\n    ['webm', 'video/webm'],\n    ['webmanifest', 'application/manifest+json'],\n    ['webp', 'image/webp'],\n    ['wg', 'application/vnd.pmi.widget'],\n    ['wgt', 'application/widget'],\n    ['wks', 'application/vnd.ms-works'],\n    ['wm', 'video/x-ms-wm'],\n    ['wma', 'audio/x-ms-wma'],\n    ['wmd', 'application/x-ms-wmd'],\n    ['wmf', 'image/wmf'],\n    ['wml', 'text/vnd.wap.wml'],\n    ['wmlc', 'application/wmlc'],\n    ['wmls', 'text/vnd.wap.wmlscript'],\n    ['wmlsc', 'application/vnd.wap.wmlscriptc'],\n    ['wmv', 'video/x-ms-wmv'],\n    ['wmx', 'video/x-ms-wmx'],\n    ['wmz', 'application/x-msmetafile'],\n    ['woff', 'font/woff'],\n    ['woff2', 'font/woff2'],\n    ['word', 'application/msword'],\n    ['wpd', 'application/vnd.wordperfect'],\n    ['wpl', 'application/vnd.ms-wpl'],\n    ['wps', 'application/vnd.ms-works'],\n    ['wqd', 'application/vnd.wqd'],\n    ['wri', 'application/x-mswrite'],\n    ['wrl', 'model/vrml'],\n    ['wsc', 'message/vnd.wfa.wsc'],\n    ['wsdl', 'application/wsdl+xml'],\n    ['wspolicy', 'application/wspolicy+xml'],\n    ['wtb', 'application/vnd.webturbo'],\n    ['wvx', 'video/x-ms-wvx'],\n    ['x3d', 'model/x3d+xml'],\n    ['x3db', 'model/x3d+fastinfoset'],\n    ['x3dbz', 'model/x3d+binary'],\n    ['x3dv', 'model/x3d-vrml'],\n    ['x3dvz', 'model/x3d+vrml'],\n    ['x3dz', 'model/x3d+xml'],\n    ['x32', 'application/x-authorware-bin'],\n    ['x_b', 'model/vnd.parasolid.transmit.binary'],\n    ['x_t', 'model/vnd.parasolid.transmit.text'],\n    ['xaml', 'application/xaml+xml'],\n    ['xap', 'application/x-silverlight-app'],\n    ['xar', 'application/vnd.xara'],\n    ['xav', 'application/xcap-att+xml'],\n    ['xbap', 'application/x-ms-xbap'],\n    ['xbd', 'application/vnd.fujixerox.docuworks.binder'],\n    ['xbm', 'image/x-xbitmap'],\n    ['xca', 'application/xcap-caps+xml'],\n    ['xcs', 'application/calendar+xml'],\n    ['xdf', 'application/xcap-diff+xml'],\n    ['xdm', 'application/vnd.syncml.dm+xml'],\n    ['xdp', 'application/vnd.adobe.xdp+xml'],\n    ['xdssc', 'application/dssc+xml'],\n    ['xdw', 'application/vnd.fujixerox.docuworks'],\n    ['xel', 'application/xcap-el+xml'],\n    ['xenc', 'application/xenc+xml'],\n    ['xer', 'application/patch-ops-error+xml'],\n    ['xfdf', 'application/vnd.adobe.xfdf'],\n    ['xfdl', 'application/vnd.xfdl'],\n    ['xht', 'application/xhtml+xml'],\n    ['xhtml', 'application/xhtml+xml'],\n    ['xhvml', 'application/xv+xml'],\n    ['xif', 'image/vnd.xiff'],\n    ['xl', 'application/excel'],\n    ['xla', 'application/vnd.ms-excel'],\n    ['xlam', 'application/vnd.ms-excel.addin.macroEnabled.12'],\n    ['xlc', 'application/vnd.ms-excel'],\n    ['xlf', 'application/xliff+xml'],\n    ['xlm', 'application/vnd.ms-excel'],\n    ['xls', 'application/vnd.ms-excel'],\n    ['xlsb', 'application/vnd.ms-excel.sheet.binary.macroEnabled.12'],\n    ['xlsm', 'application/vnd.ms-excel.sheet.macroEnabled.12'],\n    ['xlsx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],\n    ['xlt', 'application/vnd.ms-excel'],\n    ['xltm', 'application/vnd.ms-excel.template.macroEnabled.12'],\n    ['xltx', 'application/vnd.openxmlformats-officedocument.spreadsheetml.template'],\n    ['xlw', 'application/vnd.ms-excel'],\n    ['xm', 'audio/xm'],\n    ['xml', 'application/xml'],\n    ['xns', 'application/xcap-ns+xml'],\n    ['xo', 'application/vnd.olpc-sugar'],\n    ['xop', 'application/xop+xml'],\n    ['xpi', 'application/x-xpinstall'],\n    ['xpl', 'application/xproc+xml'],\n    ['xpm', 'image/x-xpixmap'],\n    ['xpr', 'application/vnd.is-xpr'],\n    ['xps', 'application/vnd.ms-xpsdocument'],\n    ['xpw', 'application/vnd.intercon.formnet'],\n    ['xpx', 'application/vnd.intercon.formnet'],\n    ['xsd', 'application/xml'],\n    ['xsl', 'application/xml'],\n    ['xslt', 'application/xslt+xml'],\n    ['xsm', 'application/vnd.syncml+xml'],\n    ['xspf', 'application/xspf+xml'],\n    ['xul', 'application/vnd.mozilla.xul+xml'],\n    ['xvm', 'application/xv+xml'],\n    ['xvml', 'application/xv+xml'],\n    ['xwd', 'image/x-xwindowdump'],\n    ['xyz', 'chemical/x-xyz'],\n    ['xz', 'application/x-xz'],\n    ['yaml', 'text/yaml'],\n    ['yang', 'application/yang'],\n    ['yin', 'application/yin+xml'],\n    ['yml', 'text/yaml'],\n    ['ymp', 'text/x-suse-ymp'],\n    ['z', 'application/x-compress'],\n    ['z1', 'application/x-zmachine'],\n    ['z2', 'application/x-zmachine'],\n    ['z3', 'application/x-zmachine'],\n    ['z4', 'application/x-zmachine'],\n    ['z5', 'application/x-zmachine'],\n    ['z6', 'application/x-zmachine'],\n    ['z7', 'application/x-zmachine'],\n    ['z8', 'application/x-zmachine'],\n    ['zaz', 'application/vnd.zzazz.deck+xml'],\n    ['zip', 'application/zip'],\n    ['zir', 'application/vnd.zul'],\n    ['zirz', 'application/vnd.zul'],\n    ['zmm', 'application/vnd.handheld-entertainment+xml'],\n    ['zsh', 'text/x-scriptzsh']\n]);\nfunction toFileWithPath(file, path, h) {\n    const f = withMimeType(file);\n    const { webkitRelativePath } = file;\n    const p = typeof path === 'string'\n        ? path\n        // If <input webkitdirectory> is set,\n        // the File will have a {webkitRelativePath} property\n        // https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/webkitdirectory\n        : typeof webkitRelativePath === 'string' && webkitRelativePath.length > 0\n            ? webkitRelativePath\n            : `./${file.name}`;\n    if (typeof f.path !== 'string') { // on electron, path is already set to the absolute path\n        setObjProp(f, 'path', p);\n    }\n    if (h !== undefined) {\n        Object.defineProperty(f, 'handle', {\n            value: h,\n            writable: false,\n            configurable: false,\n            enumerable: true\n        });\n    }\n    // Always populate a relative path so that even electron apps have access to a relativePath value\n    setObjProp(f, 'relativePath', p);\n    return f;\n}\nfunction withMimeType(file) {\n    const { name } = file;\n    const hasExtension = name && name.lastIndexOf('.') !== -1;\n    if (hasExtension && !file.type) {\n        const ext = name.split('.')\n            .pop().toLowerCase();\n        const type = COMMON_MIME_TYPES.get(ext);\n        if (type) {\n            Object.defineProperty(file, 'type', {\n                value: type,\n                writable: false,\n                configurable: false,\n                enumerable: true\n            });\n        }\n    }\n    return file;\n}\nfunction setObjProp(f, key, value) {\n    Object.defineProperty(f, key, {\n        value,\n        writable: false,\n        configurable: false,\n        enumerable: true\n    });\n}\n//# sourceMappingURL=file.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/file.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/file-selector/dist/es2015/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/file-selector/dist/es2015/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromEvent: () => (/* reexport safe */ _file_selector__WEBPACK_IMPORTED_MODULE_0__.fromEvent)\n/* harmony export */ });\n/* harmony import */ var _file_selector__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./file-selector */ \"(ssr)/./node_modules/file-selector/dist/es2015/file-selector.js\");\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmlsZS1zZWxlY3Rvci9kaXN0L2VzMjAxNS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUE0QztBQUM1QyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxtYXR0aVxcRG9jdW1lbnRzXFxjb2RpbmdcXGd1ZXNzLW15LWFnZVxcZ3Vlc3MtbXktYWdlLW5ld1xcbm9kZV9tb2R1bGVzXFxmaWxlLXNlbGVjdG9yXFxkaXN0XFxlczIwMTVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB7IGZyb21FdmVudCB9IGZyb20gJy4vZmlsZS1zZWxlY3Rvcic7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/file-selector/dist/es2015/index.js\n");

/***/ })

};
;